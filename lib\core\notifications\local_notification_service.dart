import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:bond_notifications/bond_notifications.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

class LocalNotificationService extends PushNotificationProvider {
  static final FlutterLocalNotificationsPlugin _notificationsPlugin =
      FlutterLocalNotificationsPlugin();

  // Stream controllers for notification events
  final _notificationStreamController =
      StreamController<NotificationData>.broadcast();
  final _notificationTappedStreamController =
      StreamController<NotificationData>.broadcast();
  final _tokenRefreshStreamController = StreamController<String>.broadcast();
  final _notificationDismissStreamController =
      StreamController<NotificationData>.broadcast();

  FlutterLocalNotificationsPlugin get notificationsPlugin =>
      _notificationsPlugin;

  LocalNotificationService() {
    // Initialize the local notifications plugin when created
    initialize();
  }

  Future<void> initialize() async {
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@drawable/ic_stat');

    final DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
      requestSoundPermission: true,
      requestBadgePermission: false,
      requestAlertPermission: true,
    );

    final InitializationSettings initializationSettings =
        InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );

    await _notificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse:
          (NotificationResponse notificationResponse) {
        // Handle notification tap
        debugPrint(
            'Notification tapped with payload: ${notificationResponse.payload}');

        if (notificationResponse.payload != null) {
          try {
            final payloadMap = jsonDecode(notificationResponse.payload!);
            _notificationTappedStreamController.add(payloadMap);
          } catch (e) {
            debugPrint('Error parsing notification payload: $e');
          }
        }
      },
    );
  }

  Future<void> createAndroidNotificationChannel() async {
    const AndroidNotificationChannel channel = AndroidNotificationChannel(
      'high_importance_channel', // id
      'High Importance Notifications', // title
      description: 'This channel is used for important notifications.',
      importance: Importance.max,
    );

    await _notificationsPlugin
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(channel);
  }

  Future<void> showNotification({
    required String title,
    required String body,
    String? payload,
    String? imageUrl,
  }) async {
    AndroidNotificationDetails androidPlatformChannelSpecifics =
        const AndroidNotificationDetails(
      'high_importance_channel',
      'High Importance Notifications',
      channelDescription: 'This channel is used for important notifications.',
      importance: Importance.max,
      priority: Priority.high,
      showWhen: true,
    );

    DarwinNotificationDetails iOSPlatformChannelSpecifics =
        const DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    _notificationsPlugin.pendingNotificationRequests();
    await _notificationsPlugin.show(
      DateTime.now().millisecond, // Unique ID
      title,
      body,
      platformChannelSpecifics,
      payload: payload,
    );

  }

  // Implementation of PushNotificationProvider abstract methods
  @override
  Future<NotificationData?> get initialNotification async => null;

  @override
  Stream<NotificationData> get onNotification =>
      _notificationStreamController.stream;

  @override
  Stream<NotificationData> get onNotificationTapped =>
      _notificationTappedStreamController.stream;

  @override
  Stream<String> get onTokenRefresh => _tokenRefreshStreamController.stream;

  @override
  Future<String?> get token async =>
      null; // Local notifications don't have tokens

  @override
  Future<String?> get apnsToken async =>
      null; // Local notifications don't have APNS tokens

  @override
  Future<void> deleteToken() async {
    // No token to delete for local notifications
    return;
  }

  @override
  Stream<NotificationData> get onNotificationDismiss =>
      _notificationDismissStreamController.stream;

  // Method to add a notification to the stream
  void addNotification(NotificationData data) {
    _notificationStreamController.add(data);

    // Also show the notification using the local notifications plugin
    showNotification(
      title: data['title'] as String? ?? 'New Notification',
      body: data['body'] as String? ?? '',
      // payload: jsonEncode(data),
      payload: jsonEncode(data),
    );
  }

  handelClickNotificatios(NotificationData data) {
    _notificationTappedStreamController.add(data);
  }
}

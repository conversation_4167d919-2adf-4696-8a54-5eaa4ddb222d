import 'dart:developer';

import 'package:bond_core/bond_core.dart';
import 'package:bond_form/bond_form.dart';
import 'package:bond_form_riverpod/bond_form_riverpod.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/features/auction/data/api.dart';
import 'package:zod/features/auth/auth.dart';
import 'package:zod/features/auth/data/errors/validation_error.dart';
import 'package:zod/features/home/<USER>/models/address.dart';

class AddAddressFormController
    extends AutoDisposeFormStateNotifier<Address, ValidationError> {
  final AuctionApi _api;

  AddAddressFormController(this._api);

  User get user => Auth.user();
  @override
  Map<String, FormFieldState> fields() => {
        'address': TextFieldState(
          null,
          label: 'Building Name',
          rules: [
            Rules.required(),
          ],
        ),
        'house_no_street': TextFieldState(
          null,
          label: 'Street',
          rules: [
            Rules.required(),
          ],
        ),
        'phone_number': TextFieldState(
          user.phone,
          label: appContext.localizations.filed_phone_label,
          rules: [
            Rules.required(),
          ],
        ),
        'latitude': TextFieldState(
          null,
          label: 'Latitude',
        ),
        'longitude': TextFieldState(
          null,
          label: 'Longitude',
        ),
      };

  @override
  Future<Address> onSubmit() async {
    try {
      final address = state.required().textFieldValue('address');
      final houseNoStreet = state.required().textFieldValue('house_no_street');
      final phoneNumber = state.required().textFieldValue('phone_number');

      // Get the latitude and longitude values from text fields
      final latitudeValue = state.textFieldValue('latitude');
      final longitudeValue = state.textFieldValue('longitude');

      log("address $address");
      log("phoneNumber $phoneNumber");
      log("houseNoStreet $houseNoStreet");
      log("latitudeValue $latitudeValue");
      log("longitudeValue $longitudeValue");

      // Validate latitude and longitude
      if (latitudeValue == null || latitudeValue.isEmpty) {
        throw ValidationError(
          message: 'Please select a location on the map',
          errors: {
            'latitude': ['Please select a location on the map']
          },
        );
      }

      if (longitudeValue == null || longitudeValue.isEmpty) {
        throw ValidationError(
          message: 'Please select a location on the map',
          errors: {
            'longitude': ['Please select a location on the map']
          },
        );
      }

      final body = {
        'address': address,
        'house_no_street': houseNoStreet,
        'phone_number': phoneNumber,
        'latitude': latitudeValue,
        'longitude': longitudeValue,
      };

      final response = await _api.addAddress(body);
      return response.data;
    } catch (e, s) {
      log("Error in onSubmit: $e", stackTrace: s);
      log("Error type: ${e.runtimeType}");

      // Convert any error to a ValidationError
      if (e is ValidationError) {
        // If it's already a ValidationError, rethrow it
        rethrow;
      } else {
        // Otherwise, wrap it in a ValidationError
        throw ValidationError(
          message: e.toString(),
          errors: {
            'form': [e.toString()]
          },
        );
      }
    }
  }
}

final addAddressFormProvider = NotifierProvider.autoDispose<
    AddAddressFormController, BondFormState<Address, ValidationError>>(
  () => AddAddressFormController(sl()),
);

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auction_user_activity.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AuctionUserActivity _$AuctionUserActivityFromJson(Map<String, dynamic> json) =>
    AuctionUserActivity(
      totalUserBids: (json['total_user_bids'] as num).toInt(),
      entryFee: (json['entry_fee'] as num).toInt(),
      pointsReturned: (json['points_returned'] as num).toInt(),
      isWinner: json['is_winner'] as bool,
      deductedPoints: (json['deducted_points'] as num?)?.toInt(),
      availablePoint: (json['available_point'] as num?)?.toInt(),
    );

Map<String, dynamic> _$AuctionUserActivityToJson(
        AuctionUserActivity instance) =>
    <String, dynamic>{
      'total_user_bids': instance.totalUserBids,
      'entry_fee': instance.entryFee,
      'points_returned': instance.pointsReturned,
      'is_winner': instance.isWinner,
      'deducted_points': instance.deductedPoints,
      'available_point': instance.availablePoint,
    };

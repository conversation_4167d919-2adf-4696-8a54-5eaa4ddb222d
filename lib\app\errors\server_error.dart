import 'package:json_annotation/json_annotation.dart';

part 'server_error.g.dart';

@JsonSerializable()
class ServerError extends Error {
  ServerError({
    required this.message,
     this.code,
    this.breakUntil,
  });

  final String message;
  final String? code;
 @JsonKey(name: 'break_until')
  final DateTime? breakUntil;

  factory ServerError.fromJson(Map<String, dynamic> json) =>
      _$ServerErrorFromJson(json);

  Map<String, dynamic> toJson() => _$ServerErrorToJson(this);

  @override
  String toString() => 'ServerError: $message ($code)';
}

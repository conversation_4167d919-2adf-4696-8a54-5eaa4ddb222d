import 'package:flutter/material.dart';

class DialogRoutePage<T> extends Page<T> {
  final Widget child;

  const DialogRoutePage({
    required this.child,
    super.key,
    super.name,
    super.arguments,
  });

  @override
  Route<T> createRoute(BuildContext context) {
    return DialogRoute<T>(
      builder: (BuildContext context) {
        return child;
      },
      useSafeArea: true,
      settings: this,
      context: context,
    );
  }
}

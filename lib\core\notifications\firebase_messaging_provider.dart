import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/core/notifications/push_notifications_providers/firebase_messaging_push_notification_provider.dart';

final firebaseMessagingProvider = Provider<FirebaseMessagingNotificationProvider>((ref) {
  final firebaseMessaging = FirebaseMessaging.instance;
  return FirebaseMessagingNotificationProvider(firebaseMessaging);
});

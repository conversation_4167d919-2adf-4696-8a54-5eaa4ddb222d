// ignore_for_file: deprecated_member_use

import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/app_theme.dart';
import 'package:zod/features/auction/data/models/auction.dart';

import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/core/resources/app_icons.dart';
import 'package:zod/core/widgets/number_coins_view.dart';
import 'package:zod/features/auction/presentation/views/auction_status_label.dart';
import 'package:zod/features/auction/presentation/views/joined_view.dart';
import 'category_badge_view.dart';

class HeaderAuctionDetailsView extends StatelessWidget {
  const HeaderAuctionDetailsView({super.key, required this.auction});

  final Auction auction;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: LayoutBuilder(
        builder: (context, constraints) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Flexible(
                    child: Row(
                      children: [
                        AuctionStatusLabel(status: auction.status),
                        SizedBox(width: 8),
                        if (auction.isJoined ?? false) JoinedView(),
                      ],
                    ),
                  ),
                  if (auction.category != null)
                    CategoryBadgeView(
                      title: auction.category?.name ?? '',
                    ),
                ],
              ),
              SizedBox(height: 8),
              Text(
                auction.name,
                style: context.textTheme.titleMedium,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Flexible(
                    child: Row(
                      children: [
                        Text(
                          context.localizations.price,
                          style: context.textTheme.labelLarge,
                        ),
                        SizedBox(width: 4),
                        Row(
                          children: [
                            SvgPicture.asset(AppIcons.riyal,
                                color: AppColors.primaryColor,
                                width: 14,
                                height: 14),
                            SizedBox(width: 2),
                            Text(
                              auction.productCost.toString(),
                              style: context
                                  .textTheme.labelLarge?.w700.primaryColor,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                        SizedBox(width: 4),
                        Visibility(
                          visible: auction.oldPrice != null,
                          child: Row(
                            children: [
                              SvgPicture.asset(AppIcons.riyal,
                                  color: AppColors.steelGray,
                                  width: 14,
                                  height: 14),
                              SizedBox(width: 2),
                              Text(
                                auction.oldPrice.toString(),
                                style: context
                                    .textTheme.labelLarge?.w700.darkSteel
                                    .copyWith(
                                        decoration: TextDecoration.lineThrough),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  NumberCoinsView(
                    number: auction.maxBidPoints.toString(),
                    textColor: AppColors.goldenBrown,
                  ),
                ],
              ),
              SizedBox(height: 16),
            ],
          );
        },
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

import 'package:zod/core/resources/app_colors.dart';

class ShimmerAuctionTabsView extends StatelessWidget {
  const ShimmerAuctionTabsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: _shimmerContainer(height: 30, width: double.infinity), // Tab bar
          ),
          SizedBox(height: 12),
          Divider(color: AppColors.softSilver, height: 3),
          Flexible(
            child: ListView.builder(
              itemCount: 4, // Simulating tab views
              itemBuilder: (context, index) {
                return Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                  child: _shimmerContainer(height: 100, width: double.infinity), // Tab content
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _shimmerContainer({required double height, required double width}) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        height: height,
        width: width,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
}

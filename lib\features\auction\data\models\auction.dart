import 'dart:developer' as dev;
import 'dart:math';

import 'package:bond_network/bond_network.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/utils/timezone_utils.dart';
import 'package:zod/features/auction/data/models/bid_history.dart';
import 'package:zod/features/auction/data/models/category.dart';
import 'package:zod/features/auction/data/models/participator.dart';
import 'package:zod/features/auth/auth.dart';

import '../../../auth/data/models/avatar.dart';

part '../enums/auction_status.dart';
part 'auction.g.dart';

@JsonSerializable(explicitToJson: true)
class Auction extends Model {
  final String name;
  final String? image;
  final AuctionStatus status;
  @JsonKey(name: 'start_at')
  final DateTime? startAt;
  @JsonKey(name: 'ended_at')
  final DateTime? endedAt;
  @JsonKey(name: 'open_bid_points')
  final int? openBidPoints;
  @JsonKey(name: 'max_bid_points')
  final int? maxBidPoints;
  @JsonKey(name: 'entry_fee')
  final int? entryFee;
  @JsonKey(name: 'product_cost')
  final int? productCost;
  @JsonKey(name: 'total_bid_points')
  final int? totalBidPoints;
  @JsonKey(name: 'equivalent_points')
  final int? equivalentPoints;
  @JsonKey(name: 'increment_bid_points')
  final int? incrementBidPoints;
  @JsonKey(name: 'remaining_participation')
  final int? remainingParticipation;
  @JsonKey(name: 'min_number_of_participant')
  final int? minNumberOfParticipant;
  @JsonKey(name: 'max_number_of_participant')
  final int? maxNumberOfParticipant;
  @JsonKey(name: 'direct_purchase_points')
  final int? directPurchasePoints;
  @JsonKey(name: 'start_after_hours')
  final String? startAfterHours;
  @JsonKey(name: 'start_at_time')
  final String? startAtTime;
  @JsonKey(name: 'is_joined', defaultValue: false)
  final bool? isJoined;
  @JsonKey(name: 'prize_expired', defaultValue: false)
  final bool? prizeExpired;
  @JsonKey(name: 'is_winner', defaultValue: false)
  final bool? isWinner;
  @JsonKey(name: 'winner_id', defaultValue: 0)
  final int? winnerId;
  @JsonKey(name: 'is_favorite', defaultValue: false)
  final bool? isFavorite;
  @JsonKey(name: 'is_direct_purchased', defaultValue: false)
  final bool? isDirectPurchased;
  final Category? category;
  final List<Participator>? participators;
  @JsonKey(name: 'participator_ids', defaultValue: [])
  final List<int>? participatorIds;
  final List<BidHistory>? bids;
  @JsonKey(name: 'claim_process')
  final String? claimProcess;
  @JsonKey(name: 'technical_specifications')
  final String? technicalSpecifications;
  @JsonKey(name: 'short_description')
  final String? shortDescription;
  final String? description;
  final String? rules;
  final int? duration;
  @JsonKey(name: 'old_price')
  final int? oldPrice;
  @JsonKey(name: 'enable_fee', defaultValue: false)
  final bool? enableFee;
  final List<String>? gallery;

  @JsonKey(name: 'suggestion_points', defaultValue: [])
  final List<double>? suggestionPoints;
  @JsonKey(name: 'suggestion_bids', defaultValue: [])
  final List<double>? suggestionBids;
  @JsonKey(name: 'is_reminder_enabled', defaultValue: false)
  final bool? isReminderEnabled;

  const Auction({
    required super.id,
    required this.name,
    required this.status,
    this.openBidPoints,
    this.maxNumberOfParticipant,
    this.minNumberOfParticipant,
    this.remainingParticipation,
    this.image,
    this.startAt,
    this.endedAt,
    this.isJoined,
    this.category,
    this.participators,
    this.participatorIds,
    this.bids,
    this.totalBidPoints,
    this.isWinner,
    this.entryFee,
    this.description,
    this.claimProcess,
    this.directPurchasePoints,
    this.duration,
    this.enableFee,
    this.equivalentPoints,
    this.gallery,
    this.incrementBidPoints,
    this.maxBidPoints,
    this.isDirectPurchased,
    this.productCost,
    this.rules,
    this.shortDescription,
    this.startAfterHours,
    this.startAtTime,
    this.isFavorite,
    this.suggestionPoints,
    this.technicalSpecifications,
    this.suggestionBids,
    this.oldPrice,
    this.winnerId,
    this.isReminderEnabled,
    this.prizeExpired,
  });

  factory Auction.fromJson(Map<String, dynamic> json) =>
      _$AuctionFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$AuctionToJson(this);

  @override
  List<Object?> get props => [id];

  List<String> get avatarUrls {
    if (participators == null || participators!.isEmpty) {
      return [];
    }
    return participators!
        .where((participator) => participator.avatar != null)
        .map((participator) => participator.avatar!.url)
        .toList();
  }

  factory Auction.fake() {
    final random = Random();
    final fakeNames = [
      "Tech Auction",
      "Gadget Sale",
      "Electronics Bid",
      "Vintage Collectibles",
      "Art Auction",
    ];
    final fakeCategories = [
      Category(id: 1, name: 'Electronics'),
      Category(id: 2, name: 'Collectibles'),
      Category(id: 3, name: 'Art'),
    ];
    final fakeAvatars = [
      Avatar(
        id: 50,
        url:
            "https://fls-9e65c950-1610-4e89-9a69-612c327ac9d8.laravel.cloud/avatars/01JP2GZ9ANZ845Q1FHTYH4E0B5.png",
      ),
      Avatar(
        id: 51,
        url:
            "https://fls-9e65c950-1610-4e89-9a69-612c327ac9d8.laravel.cloud/avatars/01JP2GZ9ANZ845Q1FHTYH4E0B5.png",
      ),
      null, // Some participators may have no avatar
    ];
    final fakeParticipatorNames = [
      "Admin",
      "sjxj",
      "User123",
      "CollectorX",
      "BidderPro",
    ];

    final participatorsCount = random.nextInt(4) + 1;
    final participators = List.generate(participatorsCount, (index) {
      return Participator(
        id: random.nextInt(100) + 1,
        name:
            fakeParticipatorNames[random.nextInt(fakeParticipatorNames.length)],
        avatar: fakeAvatars[random.nextInt(fakeAvatars.length)],
      );
    });

    return Auction(
      id: random.nextInt(1000) + 1,
      name: fakeNames[random.nextInt(fakeNames.length)],
      status: AuctionStatus.values[random.nextInt(AuctionStatus.values.length)],
      openBidPoints: random.nextInt(500) + 50,
      remainingParticipation: random.nextInt(10),
      category: fakeCategories[random.nextInt(fakeCategories.length)],
      totalBidPoints: random.nextInt(200) + 10,
      isWinner: random.nextBool(),
      participators: participators,
      startAt: random.nextBool()
          ? DateTime.now().add(Duration(days: random.nextInt(10)))
          : null,
      endedAt: random.nextBool()
          ? DateTime.now().subtract(Duration(days: random.nextInt(10)))
          : null,
      isJoined: random.nextBool(),
      maxNumberOfParticipant: 0,
      minNumberOfParticipant: 0,
      image: random.nextBool()
          ? "https://example.com/images/auction_${random.nextInt(100)}.png"
          : null,
    );
  }

  static List<Auction> get fakeList =>
      List.generate(8, (index) => Auction.fake());

  bool get isClosed => status == AuctionStatus.closed;

  bool get isLive => status == AuctionStatus.live;

  bool get isUpComing => status == AuctionStatus.upComing;

  bool get isAllSets => remainingParticipation == 0;

  bool get isWinnerParticipator =>
      isWinner == true && isJoined == true && isClosed;

  bool get isNotWinnerParticipator =>
      isWinner == false && isJoined == true && isClosed;

  List<Participator> get participatorsLimited {
    if (participators == null || participators!.isEmpty) {
      return [];
    }

    // Get current user ID
    final currentUserId = Auth.check() ? Auth.user().id : -1;

    // Check if current user is in the participators list
    final currentUserIndex =
        participators!.indexWhere((p) => p.id == currentUserId);

    // If current user is not in the list, just take the first 3 participators
    if (currentUserIndex == -1) {
      return participators!.take(3).toList();
    }

    // If current user is in the list, put them first
    final result = <Participator>[];

    // Add current user first
    result.add(participators![currentUserIndex]);

    // Add up to 2 more participators (excluding the current user)
    int count = 0;
    for (int i = 0; i < participators!.length && count < 2; i++) {
      if (i != currentUserIndex) {
        result.add(participators![i]);
        count++;
      }
    }

    return result;
  }

  //participators count
  int get participatorsCount {
    if (participators == null || participators!.isEmpty) {
      return 0;
    }
    return participators!.length;
  }

  Auction copyWith({
    int? id,
    String? name,
    AuctionStatus? status,
    int? openBidPoints,
    int? maxNumberOfParticipant,
    int? minNumberOfParticipant,
    int? remainingParticipation,
    String? image,
    DateTime? startAt,
    DateTime? endedAt,
    bool? isJoined,
    Category? category,
    List<Participator>? participators,
    List<int>? participatorIds,
    List<BidHistory>? bids,
    int? totalBidPoints,
    bool? isWinner,
    int? entryFee,
    String? description,
    String? claimProcess,
    int? directPurchasePoints,
    int? duration,
    bool? enableFee,
    int? equivalentPoints,
    List<String>? gallery,
    int? incrementBidPoints,
    int? maxBidPoints,
    bool? isDirectPurchased,
    int? productCost,
    String? rules,
    String? shortDescription,
    String? startAfterHours,
    String? startAtTime,
    bool? isFavorite,
    List<double>? suggestionPoints,
    String? technicalSpecifications,
    List<double>? suggestionBids,
    int? oldPrice,
    int? winnerId,
    bool? isReminderEnabled,
    bool? prizeExpired,
  }) {
    return Auction(
      id: id ?? super.id,
      name: name ?? this.name,
      status: status ?? this.status,
      openBidPoints: openBidPoints ?? this.openBidPoints,
      maxNumberOfParticipant:
          maxNumberOfParticipant ?? this.maxNumberOfParticipant,
      minNumberOfParticipant:
          minNumberOfParticipant ?? this.minNumberOfParticipant,
      remainingParticipation:
          remainingParticipation ?? this.remainingParticipation,
      image: image ?? this.image,
      startAt: startAt ?? this.startAt,
      endedAt: endedAt ?? this.endedAt,
      isJoined: isJoined ?? this.isJoined,
      category: category ?? this.category,
      participators: participators ?? this.participators,
      participatorIds: participatorIds ?? this.participatorIds,
      bids: bids ?? this.bids,
      totalBidPoints: totalBidPoints ?? this.totalBidPoints,
      isWinner: isWinner ?? this.isWinner,
      entryFee: entryFee ?? this.entryFee,
      description: description ?? this.description,
      claimProcess: claimProcess ?? this.claimProcess,
      directPurchasePoints: directPurchasePoints ?? this.directPurchasePoints,
      duration: duration ?? this.duration,
      enableFee: enableFee ?? this.enableFee,
      equivalentPoints: equivalentPoints ?? this.equivalentPoints,
      gallery: gallery ?? this.gallery,
      incrementBidPoints: incrementBidPoints ?? this.incrementBidPoints,
      maxBidPoints: maxBidPoints ?? this.maxBidPoints,
      isDirectPurchased: isDirectPurchased ?? this.isDirectPurchased,
      productCost: productCost ?? this.productCost,
      rules: rules ?? this.rules,
      shortDescription: shortDescription ?? this.shortDescription,
      startAfterHours: startAfterHours ?? this.startAfterHours,
      startAtTime: startAtTime ?? this.startAtTime,
      isFavorite: isFavorite ?? this.isFavorite,
      suggestionPoints: suggestionPoints ?? this.suggestionPoints,
      technicalSpecifications:
          technicalSpecifications ?? this.technicalSpecifications,
      suggestionBids: suggestionBids ?? this.suggestionBids,
      oldPrice: oldPrice ?? this.oldPrice,
      winnerId: winnerId ?? this.winnerId,
      isReminderEnabled: isReminderEnabled ?? this.isReminderEnabled,
      prizeExpired: prizeExpired ?? this.prizeExpired,
    );
  }

  //participators progress
  double get participatorsProgress {
    if (maxNumberOfParticipant == null ||
        maxNumberOfParticipant == 0 ||
        participatorsCount < 1) {
      return 0.2;
    }
    // print(maxNumberOfParticipant);
    // log("Progress ${participatorsCount / (maxNumberOfParticipant ?? 1)}");
    return participatorsCount / (maxNumberOfParticipant ?? 1);
  }

  // Check if auction is fully subscribed
  bool get isFullySubscribed {
    return (remainingParticipation ?? 0) <= 0;
  }

  // Get the auction start time from the backend
  DateTime? get effectiveStartTime {
    return startAt;
  }

  // Get the number of participants needed to complete the auction
  int get participantsNeeded {
    return remainingParticipation ?? 0;
  }

// is completed
  bool get isCompleted {
    return isFullySubscribed;
  }

  // Check if the current user is the first participant
  bool get isFirstParticipant {
    return participatorsCount == 1 && isJoined == true;
  }

  // Get the starting bid amount (same as openBidPoints)
  int get startBidAmount {
    return openBidPoints ?? 0;
  }

  String massegestatus(BuildContext context) {
    if (isFullySubscribed) {
      return context.localizations.bidders_list_is_completed;
    }
    if ((isJoined ?? false) && isCompleted) {
      return context.localizations.bidders_list_is_completed;
    }
    if (isJoined == false) {
      return context.localizations.get_ready_he_auction_starting_soon;
    }
    if ((isJoined ?? false) && (isFirstParticipant)) {
      return context.localizations.waiting_for_others_to_join;
    }
    if ((isJoined ?? false) &&
        !isCompleted &&
        (remainingParticipation ?? 0) > 5) {
      return context.localizations.waiting_for_others_to_join;
    }
    if ((isJoined ?? false) && !isCompleted) {
      return context.localizations
          .participants_remaining_to_complete_the_auction(
              "${remainingParticipation ?? 1}");
    }
    if (!(isJoined ?? false)) {
      return context.localizations.waiting_for_others_to_join;
    }
    return context.localizations.get_ready_he_auction_starting_soon;
  }

  // startAt formated  Monday, March 24
  String get startAtFormated {
    if (startAt == null) {
      return "";
    }
    // Use the local time for formatting
    final localStartAt = startAt!;
    return DateFormat('EEEE, MMMM d').format(localStartAt);
  }

  // flag to show timer When the auction is less than 2 hours to start, timer will show
  bool get showTimer {
    if (startAt == null) {
      return false;
    }
    final now = TimezoneUtils.getCurrentLocalTime();
    final difference = startAt!.difference(now);
    return difference.inHours < 2 && difference.isNegative == false;
  }

  bool get showBadgeNestedStartTimer {
    final now = TimezoneUtils.getCurrentLocalTime();
    return (startAt != null && startAt!.isBefore(now) || startAt == null);
  }

  bool get showBadgeNestedEndTimer {
    final now = TimezoneUtils.getCurrentLocalTime();
    return isLive &&
        (endedAt != null && endedAt!.isBefore(now) || endedAt == null);
  }

  bool get showStartCountdown => startAt != null && isUpComing;

  bool get showEndCountdown => endedAt != null && isLive;

  bool get showClosedJoinedView => isClosed && (isJoined ?? false);

  bool get showAuctionStatusLabel =>
      (showBadgeNestedStartTimer && isUpComing) ||
      showBadgeNestedEndTimer ||
      isClosed;

  bool get showBidSummary => isClosed;

  bool get showBidStartInfo => !isClosed;

  bool get lowPointsWarning =>
      Auth.check() &&
      isLive &&
      (Auth.user().availablePoint <= (incrementBidPoints ?? 0));

  //isNotJoined
  bool get isNotJoined => !(isJoined ?? false);

  /// Check if the current authenticated user has joined this auction
  /// This method uses the participatorIds array to check if the current user's ID is in the list
  /// This is more reliable than using the isJoined flag from the backend
  bool get hasCurrentUserJoined {
    // If not authenticated, user hasn't joined
    if (!Auth.check()) {
      return false;
    }

    // Get current user ID
    final currentUserId = Auth.user().id;

    // If participatorIds is null or empty, user hasn't joined
    if (participatorIds == null || participatorIds!.isEmpty) {
      return false;
    }

    // Check if current user's ID is in the participatorIds list
    return participatorIds!.contains(currentUserId);
  }

  /// Check if the auction is in its last hour (59 minutes and 59 seconds or less remaining)
  ///
  /// This method only checks the time remaining, regardless of the auction status.
  /// The hurry up notification should be shown when there's less than an hour remaining,
  /// even if the auction status is not "live".
  bool get isInLastHour {
    // If the auction has no end time, it's not in its last hour
    if (endedAt == null) {
      dev.log('AUCTION_MODEL - isInLastHour - No end time');
      return false;
    }

    // Get the current time
    final now = TimezoneUtils.getCurrentLocalTime();

    // Calculate the difference in seconds
    final differenceInSeconds =
        TimezoneUtils.getSecondsDifference(endedAt!, now);

    // Calculate hours and minutes
    final hours = differenceInSeconds ~/ 3600;
    final minutes = (differenceInSeconds % 3600) ~/ 60;
    final seconds = differenceInSeconds % 60;

    // Log the time values for debugging
    dev.log('AUCTION_MODEL - isInLastHour - End time: $endedAt');
    dev.log('AUCTION_MODEL - isInLastHour - Current time: $now');
    dev.log(
        'AUCTION_MODEL - isInLastHour - Difference in seconds: $differenceInSeconds');
    dev.log(
        'AUCTION_MODEL - isInLastHour - Hours: $hours, Minutes: $minutes, Seconds: $seconds');

    // The auction is in its last hour when there are 60 minutes or less remaining
    // but more than 0 seconds (not yet ended)
    // We want to show it when the timer is at 1:00:00 or less
    // Using 3605 seconds (1 hour + 5 seconds) as the threshold to catch edge cases
    final result = differenceInSeconds <= 3605 && differenceInSeconds > 0;
    dev.log('AUCTION_MODEL - isInLastHour - Result: $result');
    return result;
  }

  bool get isIamWinner {
    if (!Auth.check()) {
      return false;
    }
    if (winnerId == null) {
      return false;
    }
    print("winnerId ${winnerId}");
    print("winnerId ${Auth.user().id}");
    return winnerId == Auth.user().id;
  }
}

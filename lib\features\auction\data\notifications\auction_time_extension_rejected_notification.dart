import 'dart:convert';

import 'package:bond_core/bond_core.dart';
import 'package:bond_notifications/bond_notifications.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/app/routes.dart';
import 'package:zod/features/auction/presentation/providers/auction_provider.dart';
import 'package:zod/features/auction/presentation/providers/claim_prize_provider.dart';
import 'package:zod/features/auth/presentation/providers/me_provider.dart';

class AuctionTimeExtensionRejectedNotification extends PushNotification
    with ActionablePushNotification {
  AuctionTimeExtensionRejectedNotification();

  @override
  List<String> get code => ['time_extension_rejected'];
  @override
  void onNotification(NotificationData data) {
    final parsedData = jsonDecode(data['data'].toString());
    final auctionId = parsedData['auction_id'];
    final id = int.tryParse(auctionId.toString());
    final ref = ProviderScope.containerOf(appContext);
    if (id != null) {
      ref.invalidate(claimPrizeProvider(id));
      ref.invalidate(auctionProvider(id));
      ref.invalidate(meProvider);
    }
  }

  @override
  void onNotificationTapped(NotificationData data) {
          if (data.containsKey("auction_id")) {
        final auctionId = data["auction_id"];
        goRouter.push('/auction/$auctionId');
        return;
      }
    final parsedData = jsonDecode(data['data'].toString());
    final auctionId = parsedData['auction_id'];
    final id = int.tryParse(auctionId.toString());
    goRouter.push('/cliam-prize/$id');
  }
}

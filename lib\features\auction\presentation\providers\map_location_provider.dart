import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:location/location.dart';
import 'package:permission_handler/permission_handler.dart' as permission;
import 'package:zod/core/utils/custom_marker_helper.dart';

// Provider for the current map location
final mapLocationProvider =
    StateNotifierProvider<MapLocationNotifier, MapLocationState>(
  (ref) => MapLocationNotifier(),
);

// State class for the map location
class MapLocationState {
  final LatLng? currentLocation;
  final Set<Marker> markers;
  final bool isLoading;
  final String? error;

  MapLocationState({
    this.currentLocation,
    this.markers = const {},
    this.isLoading = false,
    this.error,
  });

  MapLocationState copyWith({
    LatLng? currentLocation,
    Set<Marker>? markers,
    bool? isLoading,
    String? error,
  }) {
    return MapLocationState(
      currentLocation: currentLocation ?? this.currentLocation,
      markers: markers ?? this.markers,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

// Notifier class for the map location
class MapLocationNotifier extends StateNotifier<MapLocationState> {
  MapLocationNotifier() : super(MapLocationState()) {
    // Initialize by getting the current location
    getCurrentLocation();

    // Set up a timer to keep the marker visible
    Timer.periodic(const Duration(seconds: 3), (timer) {
      if (state.currentLocation != null && state.markers.isNotEmpty) {
        // Refresh the marker to ensure it stays visible
        updateMarkerPosition(state.currentLocation!);
      }
    });
  }

  // Get the current location
  Future<void> getCurrentLocation() async {
    try {
      // Don't set isLoading to true to avoid showing the progress indicator
      state = state.copyWith(error: null);

      // Use a default location in case of errors
      // This is a fallback for when location services are not available
      // or when running in an emulator
      final defaultLocation = LatLng(24.7136, 46.6753); // Riyadh, Saudi Arabia

      // Always use the default location first to ensure the map loads
      // This ensures we have a fallback in case of any issues
      _useDefaultLocation(defaultLocation, null);

      // Add a small delay to ensure the map is initialized
      await Future.delayed(const Duration(milliseconds: 500));

      try {
        // Try using permission_handler first (more reliable)
        try {
          // Check location permission
          final status = await permission.Permission.location.status;
          if (status.isDenied) {
            // Request permission
            final result = await permission.Permission.location.request();
            if (result.isDenied || result.isPermanentlyDenied) {
              // Use default location if permission is not granted
              _useDefaultLocation(
                  defaultLocation, 'Location permission not granted');
              return;
            }
          } else if (status.isPermanentlyDenied) {
            // Permission permanently denied, use default location
            _useDefaultLocation(
                defaultLocation, 'Location permission permanently denied');
            return;
          }

          // Check if location service is enabled
          if (!await permission
              .Permission.locationWhenInUse.serviceStatus.isEnabled) {
            // Use default location if service is not enabled
            _useDefaultLocation(
                defaultLocation, 'Location services are disabled');
            return;
          }
        } catch (e) {
          // If there's an error with permission_handler, fall back to Location plugin
          // Silently fall back to Location plugin
        }

        // Fall back to Location plugin if permission_handler fails
        final location = Location();

        try {
          // Check if location service is enabled
          bool serviceEnabled = await location.serviceEnabled();
          if (!serviceEnabled) {
            try {
              serviceEnabled = await location.requestService();
              if (!serviceEnabled) {
                // Use default location if service is not enabled
                _useDefaultLocation(
                    defaultLocation, 'Location services are disabled');
                return;
              }
            } catch (e) {
              // If there's an error requesting service, continue with default location
              _useDefaultLocation(
                  defaultLocation, 'Error requesting location service: $e');
              return;
            }
          }

          // Check if permission is granted
          PermissionStatus permissionStatus;
          try {
            permissionStatus = await location.hasPermission();
            if (permissionStatus == PermissionStatus.denied) {
              try {
                permissionStatus = await location.requestPermission();
                if (permissionStatus != PermissionStatus.granted) {
                  // Use default location if permission is not granted
                  _useDefaultLocation(
                      defaultLocation, 'Location permission not granted');
                  return;
                }
              } catch (e) {
                // If there's an error requesting permission, continue with default location
                _useDefaultLocation(defaultLocation,
                    'Error requesting location permission: $e');
                return;
              }
            }
          } catch (e) {
            // If there's an error checking permission, continue with default location
            _useDefaultLocation(
                defaultLocation, 'Error checking location permission: $e');
            return;
          }

          // Get the current location
          try {
            final locationData = await location.getLocation();
            final currentLocation = LatLng(
              locationData.latitude ?? defaultLocation.latitude,
              locationData.longitude ?? defaultLocation.longitude,
            );
            BitmapDescriptor bitmapDescriptor =
                await BitmapDescriptorHelper.getBitmapDescriptorFromSvgAsset(
                    'assets/icons/map_pin.svg');
            // Create a marker at the current location
            final marker = Marker(
                markerId: const MarkerId('current_location'),
                position: currentLocation,
                // No InfoWindow - we'll use our custom tooltip instead
                icon: bitmapDescriptor);

            // Update the state with the current location and marker
            // Always use a single marker - replace any existing markers
            state = state.copyWith(
              currentLocation: currentLocation,
              markers: {marker},
              isLoading: false,
              error: null, // Clear any previous error
            );
          } catch (e) {
            // If there's an error getting location, continue with default location
            _useDefaultLocation(
                defaultLocation, 'Error getting current location: $e');
          }
        } catch (e) {
          // If there's an error with the location plugin, use the default location
          _useDefaultLocation(
              defaultLocation, 'Location plugin initialization error: $e');
        }
      } catch (locationError) {
        // If there's an error with the location plugin, use the default location
        _useDefaultLocation(
            defaultLocation, 'Location plugin error: $locationError');
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to get current location: $e',
      );
    }
  }

  // Helper method to use a default location when there's an issue with getting the actual location
  void _useDefaultLocation(LatLng defaultLocation, String? errorMessage) async {
    BitmapDescriptor bitmapDescriptor =
        await BitmapDescriptorHelper.getBitmapDescriptorFromSvgAsset(
            'assets/icons/map_pin.svg');
    final marker = Marker(
        markerId: const MarkerId('current_location'),
        position: defaultLocation,
        // No InfoWindow - we'll use our custom tooltip instead
        icon: bitmapDescriptor,
        // Make sure the marker is visible
        visible: true,
        // Add a z-index to ensure it's on top
        zIndex: 2);

    // Always use a single marker - replace any existing markers
    state = state.copyWith(
      currentLocation: defaultLocation,
      markers: {marker},
      error: errorMessage,
      // Explicitly set isLoading to false to ensure markers are visible
      isLoading: false,
    );
  }

  // Update the marker position
  Future<void> updateMarkerPosition(LatLng position) async {
    try {
      // Get the bitmap descriptor for the marker
      BitmapDescriptor bitmapDescriptor =
          await BitmapDescriptorHelper.getBitmapDescriptorFromSvgAsset(
              'assets/icons/map_pin.svg');

      // Create the marker with all properties to ensure visibility
      final marker = Marker(
        markerId: const MarkerId('current_location'),
        position: position,
        icon: bitmapDescriptor,
        // Make sure the marker is visible
        visible: true,
        // Add a z-index to ensure it's on top
        zIndex: 2,
        // Make the marker draggable to ensure it's interactive
        draggable: false,
        // Add a flat property to ensure it's rendered correctly
        flat: false,
        // Add an alpha to ensure it's fully opaque
        alpha: 1.0,
        // Add an anchor to ensure it's positioned correctly
        anchor: const Offset(0.5, 0.5),
      );

      // Always use a single marker - replace any existing markers
      state = state.copyWith(
        currentLocation: position,
        markers: {marker},
        // Explicitly set isLoading to false to ensure markers are visible
        isLoading: false,
      );
    } catch (e) {
      // If there's an error, still update the location but use a default marker
      final marker = Marker(
        markerId: const MarkerId('current_location'),
        position: position,
        // Make sure the marker is visible
        visible: true,
        // Add a z-index to ensure it's on top
        zIndex: 2,
        // Make the marker draggable to ensure it's interactive
        draggable: false,
        // Add a flat property to ensure it's rendered correctly
        flat: false,
        // Add an alpha to ensure it's fully opaque
        alpha: 1.0,
        // Add an anchor to ensure it's positioned correctly
        anchor: const Offset(0.5, 0.5),
      );

      // Always use a single marker - replace any existing markers
      state = state.copyWith(
        currentLocation: position,
        markers: {marker},
        // Explicitly set isLoading to false to ensure markers are visible
        isLoading: false,
      );
    }
  }
}

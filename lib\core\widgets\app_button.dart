import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';

import '../resources/app_colors.dart';

class AppButton extends StatelessWidget {
  const AppButton({
    super.key,
    required this.title,
    this.enabled = true,
    this.loading = false,
    this.customChild,
    this.backgroundColor = AppColors.primaryColor,
    this.textColor = AppColors.softWhite,
    this.borderColor,
    required this.onPressed,
  });

  final String title;
  final bool enabled;
  final bool loading;
  final VoidCallback onPressed;
  final Color backgroundColor;
  final Color? textColor;
  final Color? borderColor;
  final Widget? customChild;

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
        onPressed: !loading && enabled ? onPressed : null,
        style: ButtonStyle(
          backgroundColor: WidgetStateProperty.resolveWith<Color?>((states) {
            return enabled ? backgroundColor : AppColors.softWhite;
          }),
          side: WidgetStateProperty.resolveWith<BorderSide?>((states) {
            if (!enabled) {
              return BorderSide(color: AppColors.lavenderMist, width: 1);
            }
            if (borderColor != null) {
              return BorderSide(color: borderColor!, width: 1);
            }
            return null;
          }),
          shadowColor: WidgetStateProperty.resolveWith<Color?>((states) {
            if (!enabled) {
              return AppColors.gunmetalGrayLight;
            }
            return null;
          }),
          elevation: WidgetStateProperty.resolveWith<double?>((states) {
            return enabled ? null : 2;
          }),
        ),
        child: loading
            ? const CircularProgressIndicator(
                strokeWidth: 4,
                color: AppColors.borderColor,
                backgroundColor: AppColors.lighterGray,
              )
            : customChild ??
                Text(
                  title,
                  style: !enabled
                      ? context.textTheme.labelLarge
                          ?.copyWith(color: AppColors.lightSlateGray)
                      : context.textTheme.labelLarge
                          ?.copyWith(color: textColor),
                ));
  }
}

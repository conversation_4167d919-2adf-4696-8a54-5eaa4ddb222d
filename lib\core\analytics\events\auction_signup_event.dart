import 'package:bond_app_analytics/bond_app_analytics.dart';

class AuctionSignupEvent extends AnalyticsEvent {
  final int auctionId;
  final String auctionName;
  final double entryFee;

  AuctionSignupEvent({
    required this.auctionId,
    required this.auctionName,
    required this.entryFee,
  });

  @override
  String get key => 'auction_signup';

  @override
  Map<String, dynamic> get params => {
        'auction_id': auctionId,
        'auction_name': auctionName,
        'entry_fee': entryFee,
      };
}

import 'dart:developer';
import 'dart:io';

import 'package:bond_network/bond_network.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';

class FcmTokenApi {
  final BondFire _bondFire;
  final DeviceInfoPlugin _deviceInfo;
  PackageInfo? _packageInfo;

  FcmTokenApi(this._bondFire, this._deviceInfo, this._packageInfo);

  /// Update the FCM token on the server
  Future<bool> updateToken(String fcmToken) async {
    try {
      final deviceInfo = await _getDeviceInfo();

      log(fcmToken, name: 'FCM TOKEN');
      final response = await _bondFire
          .post<SuccessResponse>('/fcm/token')
          .body({
            'fcm_token': fcmToken,
            'device_os': deviceInfo['device_os'],
            'device_os_version': deviceInfo['device_os_version'],
            'device_manufacturer': deviceInfo['device_manufacturer'],
            'device_model': deviceInfo['device_model'],
            'app_version': deviceInfo['app_version'],
            'device_language': deviceInfo['device_language'],
          })
          .header(Api.headers())
          .factory(SuccessResponse.fromJson)
          .errorFactory(ServerError.fromJson)
          .execute();

      log('FCM token updated successfully: ${response.message}');
      return true;
    } catch (e) {
      log('Failed to update FCM token: $e');
      return false;
    }
  }

  /// Get device information for FCM token registration
  Future<Map<String, String>> _getDeviceInfo() async {
    final Map<String, String> deviceData = {};

    // Initialize PackageInfo if not already initialized
    _packageInfo ??= await PackageInfo.fromPlatform();

    // Get app version
    deviceData['app_version'] =
        '${_packageInfo?.version ?? '1.0.0'}+${_packageInfo?.buildNumber ?? '1'}';

    // Get device language
    deviceData['device_language'] = Platform.localeName.split('_')[0];

    try {
      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo.androidInfo;
        deviceData['device_os'] = 'Android';
        deviceData['device_os_version'] = androidInfo.version.release;
        deviceData['device_manufacturer'] = androidInfo.manufacturer;
        deviceData['device_model'] = androidInfo.model;
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfo.iosInfo;
        deviceData['device_os'] = 'iOS';
        deviceData['device_os_version'] = iosInfo.systemVersion;
        deviceData['device_manufacturer'] = 'Apple';
        deviceData['device_model'] = iosInfo.model;
      } else {
        deviceData['device_os'] = 'Unknown';
        deviceData['device_os_version'] = 'Unknown';
        deviceData['device_manufacturer'] = 'Unknown';
        deviceData['device_model'] = 'Unknown';
      }
    } catch (e) {
      log('Error getting device info: $e');
      deviceData['device_os'] = 'Unknown';
      deviceData['device_os_version'] = 'Unknown';
      deviceData['device_manufacturer'] = 'Unknown';
      deviceData['device_model'] = 'Unknown';
    }

    return deviceData;
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/app_theme.dart';
import 'package:zod/features/auction/data/models/auction.dart';
import 'package:zod/features/auction/presentation/providers/auction_provider.dart';
import 'package:zod/features/auction/data/states/auction_state.dart';
import 'package:zod/features/auction/presentation/views/auction_details/bid_history_view.dart';
import 'package:zod/features/auction/presentation/views/auction_details/bid_placement_view.dart';

class LiveAuctionView extends ConsumerWidget {
  final Auction auction;

  const LiveAuctionView({
    super.key,
    required this.auction,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final auctionState = ref.watch(auctionProvider(auction.id));

    return Column(
      children: [
        // // Auction timer
        // if (auction.endedAt != null)
        //   Padding(
        //     padding: const EdgeInsets.all(16.0),
        //     child: Column(
        //       children: [
        //         Text(
        //           context.localizations.auction_ends_in,
        //           style: Theme.of(context).textTheme.bodyMedium,
        //         ),
        //         SizedBox(height: 8),
        //         // Use a simple Text widget for now
        //         Text(
        //           'Ends soon',
        //           style: Theme.of(context).textTheme.bodyMedium,
        //         ),
        //       ],
        //     ),
        //   ),

        // Current position indicator
        _buildPositionIndicator(context, auctionState),

        // Bid history
        Expanded(
          child: BidHistoryView(auction: auction),
        ),

        // Text("SSSSS"),
        // Bid placement component (only if user has joined)
        if (auction.isJoined == true || auction.hasCurrentUserJoined)
          BidPlacementView(auction: auction),
      ],
    );
  }

  Widget _buildPositionIndicator(
      BuildContext context, AuctionState auctionState) {
    return Container(
      padding: EdgeInsets.all(16),
      child: Row(
        children: [
          if (auctionState.bids.isNotEmpty)
            Container(
              padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              child: Text(
                context.localizations.total_bids(
                    (auctionState.auctionMeta?.bidsCount ?? 0).toString()),
                style: Theme.of(context).textTheme.bodySmall?.medium,
              ),
            ),
        ],
      ),
    );
  }
}

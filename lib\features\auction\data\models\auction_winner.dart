import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:bond_core/bond_core.dart';

part 'auction_winner.g.dart';

@JsonSerializable(explicitToJson: true)
class AuctionWinner extends Equatable with Jsonable {
  final int id;
  final String name;
  final String avatar;

  const AuctionWinner({
    required this.id,
    required this.name,
    required this.avatar,
  });

  factory AuctionWinner.fromJson(Map<String, dynamic> json) =>
      _$AuctionWinnerFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$AuctionWinnerToJson(this);

  @override
  List<Object?> get props => [id, name, avatar];
}

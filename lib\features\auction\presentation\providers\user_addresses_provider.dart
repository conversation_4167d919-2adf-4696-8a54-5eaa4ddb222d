import 'package:bond_core/bond_core.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/features/auction/data/api.dart';
import 'package:zod/features/auction/data/fake_data/fake_claim_prize_data.dart';
import 'package:zod/features/home/<USER>/models/address.dart';

final userAddressesProvider =
    FutureProvider.autoDispose<List<Address>>((ref) async {
  // Add a delay to show the shimmer effect (remove in production)
  await Future.delayed(const Duration(seconds: 2));

  try {
    // API call for production
    final api = sl<AuctionApi>();
    final response = await api.getUserAddresses();
    return response.data;
  } catch (e) {
    // Fallback to fake data if API call fails
    return FakeClaimPrizeData.getFakeAddresses();
  }
});

import 'package:bond_app_analytics/bond_app_analytics.dart';

class SignupConfirmedEvent extends AnalyticsEvent with UserSignedUp {
  final int userId;
  final String signupMethod;

  SignupConfirmedEvent({
    required this.userId,
    required this.signupMethod,
  });

  @override
  String get key => 'signup_confirmed';

  @override
  Map<String, dynamic> get params => {
        'signup_method': signupMethod,
      };

  @override
  int get id => userId;
}

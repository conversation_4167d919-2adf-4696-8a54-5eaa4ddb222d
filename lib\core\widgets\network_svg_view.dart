import 'package:cached_network_svg_image/cached_network_svg_image.dart';
import 'package:flutter/material.dart';

class NetworkSvgView extends StatelessWidget {
  const NetworkSvgView(
    this.image, {
    super.key,
    this.height,
    this.width,
    this.fit,
    this.radius = 12,
  });

  final String image;
  final double? height;
  final double? width;
  final BoxFit? fit;
  final double radius;

  @override
  Widget build(BuildContext context) {
    return CachedNetworkSVGImage(
      image,
      height: height,
      width: width,
      placeholder: Container(
        height: height,
        width: width,
        color: Colors.grey[200],
      ),
      errorWidget: const Icon(Icons.error, color: Colors.red),
      fadeDuration: const Duration(milliseconds: 500),
    );
  }
}

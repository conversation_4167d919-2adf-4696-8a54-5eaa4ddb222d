class BidUser {
  final int id;
  final String name;
  final String? image;

  BidUser({required this.id, required this.name, this.image});
  factory BidUser.fromJson(Map<String, dynamic> json) {
    return BidUser(
      id: json['id'],
      name: json['name'],
      image: json['image'],
    );
  }
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'image': image,
    };
  }
}

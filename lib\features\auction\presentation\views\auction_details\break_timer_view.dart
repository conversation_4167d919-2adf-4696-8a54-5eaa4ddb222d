import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/core/app_localizations.dart';

import 'package:zod/core/resources/app_colors.dart';

import 'package:zod/features/auction/presentation/providers/timer_provider.dart';

class BreakTimerView extends ConsumerWidget {
  final DateTime breakUntil;
  

  const BreakTimerView({
    super.key,
    required this.breakUntil,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final timerState = ref.watch(countdownTimerProvider(breakUntil));

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 8),
      decoration: BoxDecoration(
        color: AppColors.progressBarColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppColors.progressBarColor),
      ),
      child: Column(
        children: [
          Text(
            context.localizations.takeAShortBreak,
            style: context.textTheme.titleMedium?.copyWith(
              color: AppColors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            ref
                .read(countdownTimerProvider(breakUntil).notifier)
                .formatDuration(timerState.remainingTime),
            style: context.textTheme.titleMedium?.copyWith(
              color: AppColors.white,
              fontWeight: FontWeight.w600,
            ),
          )
       ,
        ],
      ),
    );
  }
}

import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:bond_core/bond_core.dart';

part 'auction_user_activity.g.dart';

@JsonSerializable(explicitToJson: true)
class AuctionUserActivity extends Equatable with Jsonable {
  @JsonKey(name: 'total_user_bids')
  final int totalUserBids;

  @<PERSON>son<PERSON>ey(name: 'entry_fee')
  final int entryFee;

  @<PERSON>sonKey(name: 'points_returned')
  final int pointsReturned;

  @<PERSON>sonKey(name: 'is_winner')
  final bool isWinner;

  @JsonKey(name: 'deducted_points')
  final int? deductedPoints;

  @JsonKey(name: 'available_point')
  final int? availablePoint;

  const AuctionUserActivity({
    required this.totalUserBids,
    required this.entryFee,
    required this.pointsReturned,
    required this.isWinner,
    this.deductedPoints,
    this.availablePoint,
  });

  factory AuctionUserActivity.from<PERSON>son(Map<String, dynamic> json) =>
      _$AuctionUserActivityFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$AuctionUserActivityToJson(this);

  @override
  List<Object?> get props =>
      [totalUserBids, entryFee, pointsReturned, isWinner, deductedPoints];
}

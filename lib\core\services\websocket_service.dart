import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:pusher_channels_flutter/pusher_channels_flutter.dart';

import '../../config/configs.dart';

class WebSocketService {
  PusherChannelsFlutter pusher = PusherChannelsFlutter.getInstance();

  final Map<String, PusherChannel> channels = {};
  final Map<String, StreamController<Map<String, dynamic>>> _eventControllers =
      {};

  bool _isConnected = false;
  bool get isConnected => _isConnected;
//check is connected

  // Singleton pattern
  static final WebSocketService _instance = WebSocketService._internal();
  factory WebSocketService() => _instance;
  WebSocketService._internal();

  Future<void> initialize() async {
    try {
      await pusher.init(
        apiKey: config('PUSHER_APP_KEY'),
        cluster: config('PUSHER_APP_CLUSTER'),
        onConnectionStateChange: (String currentState, String previousState) {
          log("pusher urrent State: $currentState, Previous State: $previousState");
          currentState == 'connected'
              ? _isConnected = true
              : _isConnected = false;
        },
        onError: (String message, int? code, dynamic error) {
          log("pusher ERROR: $message");
        },
        onSubscriptionSucceeded: (String channelName, dynamic data) {
          log("pusher Subscription to $channelName succeeded");
        },
        onSubscriptionError: (String message, dynamic error) {
          log("pusher Subscription error: $message");
          
        },
        onEvent: (event) {
          final eventKey = '${event.channelName}:${event.eventName}';

          log("pusher LISTNR Count ${_eventControllers.length}");

          log("pusher Event: ${event.eventName}  Key $eventKey  - ${event.data}");
          _eventControllers.forEach((key, value) {
            log("pusher LISTNR $key");
          });
          if (_eventControllers.containsKey(eventKey)) {
            _eventControllers[eventKey]?.add(jsonDecode(event.data));
            log("pusher Event: SendToStream");
          }

          //   eventController?.add({
          //   "channel": event.channelName,
          //   "event": event.eventName,
          //   "data": jsonDecode(event.data)
          // });
        },

        // authEndpoint: "<Your Authendpoint>",
        // onAuthorizer: onAuthorizer
      );
    } catch (e) {
      log("ERROR: $e");
    }
  }

  Future<void> subscribeToChannel(String channelName) async {
    // if (_pusher == null) {
    //   await initialize();
    // }

    channels[channelName] = await pusher.subscribe(channelName: channelName);
    await pusher.connect();
    log('pusher Subscribed to channel: $channelName');
  }

  Future<void> unsubscribeFromChannel(String channelName) async {
    try {
      await pusher.unsubscribe(channelName: channelName);
      channels.remove(channelName);
      log('pusher Unsubscribed from channel: $channelName');
    } catch (e) {
      log('pusher Error unsubscribing from channel $channelName: $e');
    }
  }

  Stream<Map<String, dynamic>> listenToEvent(
      String channelName, String eventName) {
    log("pusher event  $channelName   event $eventName");
    final eventKey = '$channelName:$eventName';

    if (!_eventControllers.containsKey(eventKey)) {
      _eventControllers[eventKey] =
          StreamController<Map<String, dynamic>>.broadcast();

      // Subscribe to channel if not already subscribed
      if (!channels.containsKey(channelName)) {
        subscribeToChannel(channelName);
      }

      // Bind to event
      final channel = channels[channelName];
      if (channel != null) {
// channel.bind(eventName, (data) {
//   log('pusher event received - $eventName - $data');
//    try {
//             final map = jsonDecode("$data") as Map<String, dynamic>;
//             _eventControllers[eventKey]?.add(map);
//           } catch (e) {
//             log('pusher Error parsing event data: $e');
//           }
// });
        // channel.bind(eventName).listen((event) {

        // });
      }
    }

    return _eventControllers[eventKey]!.stream;
  }

  void dispose() {
    for (final controller in _eventControllers.values) {
      controller.close();
    }
    _eventControllers.clear();

    channels.clear();

    pusher.disconnect();
  }
}

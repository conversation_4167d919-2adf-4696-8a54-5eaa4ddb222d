import 'dart:math' as math;

import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:zod/core/app_localizations.dart';

import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/core/resources/app_icons.dart';
import 'package:zod/features/app/app_providers.dart';
import 'package:zod/features/auction/data/models/auction.dart';

import 'package:zod/app/routes.dart';
import 'footer_not_winner_item_view.dart';
import 'footer_participator_item_view.dart';
import 'footer_winner_auction_view.dart';

class FooterAuctionItemView extends ConsumerWidget {
  const FooterAuctionItemView({
    super.key,
    required this.auction,
    this.isHome = false,
  });

  final Auction auction;
  final bool isHome;

  @override
  Widget build(BuildContext context, ref) {
    return Container(
      height: 52,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(16),
          bottomRight: Radius.circular(16),
        ),
        color: isHome ? Colors.transparent : AppColors.disabled,
      ),
      padding: EdgeInsets.symmetric(horizontal: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          auction.isWinnerParticipator
              ? FooterWinnerAuctionView()
              : auction.isNotWinnerParticipator
                  ? FooterNotWinnerItemView()
                  : FooterParticipatorItemView(auction: auction),
          InkWell(
            onTap: () => goRouter.push('/auction/${auction.id}'),
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 8, vertical: 8.5),
              decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: AppColors.softSteel),
              ),
              child: Row(
                children: [
                  Text(
                    context.localizations.more_details,
                    style: context.textTheme.labelLarge,
                  ),
                  SizedBox(width: 4),
                  ref
                          .read(localProvider)
                          .languageCode
                          .toLowerCase()
                          .contains("en")
                      ? SvgPicture.asset(AppIcons.arrow)
                      : Transform.rotate(
                          angle: math.pi,
                          child: SvgPicture.asset(AppIcons.arrow),
                        ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

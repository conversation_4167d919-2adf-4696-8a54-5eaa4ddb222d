import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:zod/core/app_theme.dart';
import 'package:zod/core/resources/app_colors.dart';

import '../resources/app_icons.dart';

class NumberCoinsView extends StatelessWidget {
  const NumberCoinsView(
      {super.key, required this.number, this.textColor = AppColors.textColor});

  final String number;
  final Color textColor;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsetsDirectional.only(
        top: 2,
        bottom: 2,
        start: 2.5,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(26),
        color: AppColors.ivory,
      ),
      child: Row(
        children: [
          Skeleton.ignore(
            child: SvgPicture.asset(AppIcons.coin),
          ),
          const SizedBox(width: 2),
          Text(
            number,
            style: context.textTheme.labelMedium?.w700.copyWith(color:textColor),
          ),
        ],
      ),
    );
  }
}

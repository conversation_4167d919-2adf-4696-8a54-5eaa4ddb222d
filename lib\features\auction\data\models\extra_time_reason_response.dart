import 'package:bond_network/bond_network.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:zod/features/auction/data/models/extra_time_reason.dart';

part 'extra_time_reason_response.g.dart';

@JsonSerializable(explicitToJson: true)
class ExtraTimeReasonResponse extends Model {
  final List<ExtraTimeReason> reasons;
  final List<int> hours;

  const ExtraTimeReasonResponse({
    required this.reasons,
    required this.hours,
  }) : super(id: 0);

  factory ExtraTimeReasonResponse.fromJson(Map<String, dynamic> json) {
    // Handle the nested structure where data contains reasons and hours
    if (json.containsKey('data')) {
      return _$ExtraTimeReasonResponseFromJson(json['data'] as Map<String, dynamic>);
    }
    return _$ExtraTimeReasonResponseFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() => _$ExtraTimeReasonResponseToJson(this);

  @override
  List<Object?> get props => [reasons, hours];
}

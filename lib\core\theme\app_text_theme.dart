import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:zod/core/resources/app_colors.dart';

final appTextTheme = TextTheme(
  displayLarge:
      GoogleFonts.ibmPlexSansArabic(fontSize: 57, fontWeight: FontWeight.bold),
  displayMedium:
      GoogleFonts.ibmPlexSansArabic(fontSize: 45, fontWeight: FontWeight.bold),
  displaySmall:
      GoogleFonts.ibmPlexSansArabic(fontSize: 36, fontWeight: FontWeight.bold),
  headlineLarge:
      GoogleFonts.ibmPlexSansArabic(fontSize: 32, fontWeight: FontWeight.w600),
  headlineMedium:
      GoogleFonts.ibmPlexSansArabic(fontSize: 28, fontWeight: FontWeight.w600),
  headlineSmall:
      GoogleFonts.ibmPlexSansArabic(fontSize: 24, fontWeight: FontWeight.w600),
  titleLarge:
      GoogleFonts.ibmPlexSansArabic(fontSize: 20, fontWeight: FontWeight.w700),
  titleMedium:
      GoogleFonts.ibmPlexSansArabic(fontSize: 18, fontWeight: FontWeight.w700),
  titleSmall:
      GoogleFonts.ibmPlexSansArabic(fontSize: 16, fontWeight: FontWeight.w500),
  bodyLarge: GoogleFonts.ibmPlexSansArabic(
      fontSize: 16, fontWeight: FontWeight.normal),
  bodyMedium: GoogleFonts.ibmPlexSansArabic(
      fontSize: 14, fontWeight: FontWeight.normal),
  bodySmall: GoogleFonts.ibmPlexSansArabic(
      fontSize: 12, fontWeight: FontWeight.normal),
  labelLarge:
      GoogleFonts.ibmPlexSansArabic(fontSize: 14, fontWeight: FontWeight.w500),
  labelMedium:
      GoogleFonts.ibmPlexSansArabic(fontSize: 12, fontWeight: FontWeight.w500),
  labelSmall:
      GoogleFonts.ibmPlexSansArabic(fontSize: 10, fontWeight: FontWeight.w500),
);

extension XTextStyle on TextStyle {
  TextStyle get primaryColor => copyWith(color: AppColors.primaryColor);

  TextStyle get royalBlue => copyWith(color: AppColors.royalBlue);

  TextStyle get black => copyWith(color: Colors.black);

  TextStyle get white => copyWith(color: Colors.white);

  TextStyle get darkRed => copyWith(color: AppColors.darkRed);

  TextStyle get darkBlue => copyWith(color: AppColors.darkBlue);

  TextStyle get deepBlue => copyWith(color: AppColors.deepBlue);

  TextStyle get darkGreen => copyWith(color: AppColors.darkGreen);

  TextStyle get deepGreen => copyWith(color: AppColors.deepGreen);

  TextStyle get textColor => copyWith(color: AppColors.textColor);

  TextStyle get crimsonRed => copyWith(color: AppColors.crimsonRed);

  TextStyle get tomatoRed => copyWith(color: AppColors.tomatoRed);

  TextStyle get slateGray => copyWith(color: AppColors.slateGray);

  TextStyle get lightGray => copyWith(color: AppColors.lightGray);

  TextStyle get steelGray => copyWith(color: AppColors.steelGray);

  TextStyle get red => copyWith(color: AppColors.red);

  TextStyle get goldenBrown => copyWith(color: AppColors.goldenBrown);

  TextStyle get darkSteel => copyWith(color: AppColors.darkSteel);

  TextStyle get charcoalGray => copyWith(color: AppColors.charcoalGray);

  TextStyle get w700 => copyWith(fontWeight: FontWeight.w700);

  TextStyle get w400 => copyWith(fontWeight: FontWeight.w400);
  TextStyle get medium => copyWith(fontWeight: FontWeight.w500);
  TextStyle get bold => copyWith(fontWeight: FontWeight.bold);
  TextStyle get underLine => copyWith(
        decoration: TextDecoration.underline,
        decorationThickness: 1,
      );
}

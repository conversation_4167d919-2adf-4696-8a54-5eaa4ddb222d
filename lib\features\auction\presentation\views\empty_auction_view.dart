import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:zod/app/routes.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/features/home/<USER>/home_page.dart';

class EmptyAuctionsView extends StatelessWidget {
  const EmptyAuctionsView({
    super.key,
    required this.title,
    required this.description,
    required this.icon,
  });

  final String icon;
  final String title;
  final String description;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset(icon),
          SizedBox(height: 16),
          Text(title, style: context.textTheme.titleLarge),
          SizedBox(height: 2),
          Text(
            description,
            style: context.textTheme.bodyLarge,
            textAlign: TextAlign.center,
          ),
          <PERSON>zed<PERSON><PERSON>(height: 44),
          InkWell(
            onTap: () => goRouter.go(HomePage.route),
            child: Container(
              width: 175,
              padding: EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: AppColors.softWhite,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: AppColors.softSteel),
              ),
              child: Center(
                child: Text(
                  context.localizations.explore_auctions,
                  style: context.textTheme.labelLarge,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

import 'dart:async';
import 'package:flutter/foundation.dart';

class CountDownUtils {
  late ValueNotifier<int> remainingSeconds;
  Timer? _timer;

  CountDownUtils(String? initialCountdown) {
    remainingSeconds = ValueNotifier<int>(_extractSeconds(initialCountdown));
    if (remainingSeconds.value > 0) {
      _startCountdown();
    }
  }

  int _extractSeconds(String? timeString) {
    if (timeString == null || timeString.isEmpty) return 0;

    final RegExp regex = RegExp(r'(\d+):(\d+)');
    final Match? match = regex.firstMatch(timeString);
    if (match != null) {
      int minutes = int.parse(match.group(1)!);
      int seconds = int.parse(match.group(2)!);
      return (minutes * 60) + seconds;
    }
    return 0;
  }

  void _startCountdown() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (remainingSeconds.value > 0) {
        remainingSeconds.value--;
      } else {
        timer.cancel();
      }
    });
  }

  void restartCountdown(String? newCountdown) {
    _timer?.cancel();
    int newSeconds = _extractSeconds(newCountdown);
    if (remainingSeconds.value != newSeconds && newSeconds > 0) {
      remainingSeconds.value = newSeconds;
      _startCountdown();
    }
  }

  String formatTime(int seconds) {
    final int minutes = seconds ~/ 60;
    final int remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  void dispose() {
    _timer?.cancel();
    remainingSeconds.dispose();
  }
}

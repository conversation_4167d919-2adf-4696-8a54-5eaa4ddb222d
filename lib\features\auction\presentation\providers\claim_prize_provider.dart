import 'dart:developer';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:bond_core/bond_core.dart';
import 'package:zod/features/auction/data/api.dart';
import 'package:zod/features/auction/data/models/claim_prize.dart';
import 'package:zod/features/home/<USER>/models/address.dart';

import '../../data/states/claim_prize_state.dart';

final claimPrizeProvider = AutoDisposeStateNotifierProvider.family<
    ClaimPrizeNotifier, ClaimPrizeState, int>(
  (ref, auctionId) => ClaimPrizeNotifier(auctionId: auctionId),
);

class ClaimPrizeNotifier extends StateNotifier<ClaimPrizeState> {
  final int auctionId;
  final AuctionApi _auctionApi = sl<AuctionApi>();

  ClaimPrizeNotifier({required this.auctionId})
      : super(ClaimPrizeState.initial()) {
    fetchClaimPrizeData();
  }

  Future<void> fetchClaimPrizeData() async {
    try {
      state = state.copyWith(isLoading: true);

      // API call to get claim prize data
      final response = await _auctionApi.getClaimPrizeData(auctionId);

      log("claim response.data ${response.data}");
      state = state.copyWith(
        claimPrize: response.data,
        isLoading: false,
        error: null,
      );
    } catch (e) {
      // Handle all exceptions
      String errorMessage;

      if (e.toString().contains('ServerError')) {
        // Try to extract the server error message
        try {
          final errorString = e.toString();
          final startIndex =
              errorString.indexOf('ServerError: ') + 'ServerError: '.length;
          final endIndex = errorString.lastIndexOf('(');
          errorMessage = errorString.substring(startIndex, endIndex).trim();
        } catch (_) {
          errorMessage = 'Server error occurred';
        }
      } else {
        // Handle other exceptions
        errorMessage = 'Failed to load claim prize data: ${e.toString()}';
      }

      state = state.copyWith(
        isLoading: false,
        error: errorMessage,
      );
    }
  }

  Future<void> selectAddress(Address address) async {
    state = state.copyWith(selectedAddress: address);

    // Update the claim prize with the selected address
    if (state.claimPrize != null) {
      final updatedClaimPrize = ClaimPrize(
        id: state.claimPrize!.id,
        name: state.claimPrize!.name,
        image: state.claimPrize!.image,
        productCost: state.claimPrize!.productCost,
        finalBidPoints: state.claimPrize!.finalBidPoints,
        pointsValue: state.claimPrize!.pointsValue,
        remainingToPay: state.claimPrize!.remainingToPay,
        taxPercentage: state.claimPrize!.taxPercentage,
        taxAmount: state.claimPrize!.taxAmount,
        totalToPay: state.claimPrize!.totalToPay,
        claimBefore: state.claimPrize!.claimBefore,
      );

      state = state.copyWith(claimPrize: updatedClaimPrize);
    }
  }
}

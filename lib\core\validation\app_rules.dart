import 'package:bond_core/bond_core.dart';
import 'package:bond_form/bond_form.dart';
import 'package:zod/core/app_localizations.dart';

/// A wrapper around the [Rules] class that adds translation support
class AppRules {
  /// Returns a required rule with a translated message
  static dynamic required({String? message}) {
    return Rules.required(
      message: message ?? appContext.localizations.field_validator_required,
    );
  }

  /// Returns a phone number rule with a translated message
  static dynamic phoneNumber({String? message}) {
    // This is a custom rule that's implemented in the project
    // We're keeping the same interface for consistency
    return Rules.regex(
      RegExp(r'^\d{9,10}$'),
      message: message ?? appContext.localizations.invalid_mobile_number,
    );
  }

  /// Returns an email rule with a translated message
  static dynamic email({String? message}) {
    return Rules.email(
      message: message ?? appContext.localizations.field_validator_email,
    );
  }

  /// Returns a min length rule with a translated message
  static dynamic minLength(int length, {String? message}) {
    return Rules.minLength(
      length,
      message: message ?? "Minimum length is $length",
    );
  }

  /// Returns a max length rule with a translated message
  static dynamic maxLength(int length, {String? message}) {
    return Rules.maxLength(
      length,
      message: message ?? "Maximum length is $length",
    );
  }

  /// Returns a numeric rule with a translated message
  static dynamic numeric({String? message}) {
    return Rules.numeric(
      message: message ?? "Must be a number",
    );
  }

  /// Returns a regex rule with a translated message
  static dynamic regex(RegExp pattern, {String? message}) {
    return Rules.regex(
      pattern,
      message: message ?? "Invalid format",
    );
  }

  /// Returns a confirm password rule with a translated message
  static dynamic confirmPassword(String otherField, {String? message}) {
    return Rules.same(
      otherField: otherField,
      message:
          message ?? appContext.localizations.field_validator_confirm_password,
    );
  }
}

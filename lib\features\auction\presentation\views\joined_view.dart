// ignore_for_file: deprecated_member_use

import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/app_theme.dart';

import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/core/resources/app_icons.dart';

class JoinedView extends StatelessWidget {
  const JoinedView({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: AppColors.borderColor,
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 8,
          vertical: 4,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,

          children: [
            SvgPicture.asset(
              AppIcons.check,
              width: 12,
              height: 12,
              color: AppColors.white,
            ),
            SizedBox(width: 4),
            Text(
              context.localizations.joined,
              style: context.textTheme.labelMedium?.white,
            ),
          ],
        ),
      ),
    );
  }
}

import 'dart:developer';

import 'package:bond_core/bond_core.dart';
import 'package:bond_form/bond_form.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/core/resources/app_icons.dart';
import 'package:zod/features/auction/data/models/extra_time_reason.dart';
import 'package:zod/features/auction/presentation/providers/extra_time_reason_provider.dart';

class ReasonDropdown extends StatelessWidget {
  const ReasonDropdown({
    super.key,
    required this.reasonItems,
    required this.hintText,
    this.errorText,
    required this.selectedValue,
    required this.onChanged,
  });

  final ExtraTimeReason? selectedValue;
  final AsyncValue<DropDownItemsState<ExtraTimeReason>> reasonItems;
  final String hintText;
  final String? errorText;
  final ValueChanged<ExtraTimeReason?> onChanged;

  @override
  Widget build(BuildContext context) {
    log("reasonItems ${reasonItems}");
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(
          color: AppColors.lightGray,
          width: 1.0,
        ),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<ExtraTimeReason?>(
          dropdownColor: AppColors.white,
          isExpanded: true,
          padding: EdgeInsets.symmetric(horizontal: 16),
          value: selectedValue,
          hint: Text(
            hintText,
            style: context.textTheme.bodyMedium,
          ),
          icon: SvgPicture.asset(AppIcons.downArraw),
          onChanged: onChanged,
          items: reasonItems.maybeWhen(
            data: (items) => items
                .map((item) => DropdownMenuItem<ExtraTimeReason?>(
                      value: item.value,
                      child: Text(
                        item.label,
                        style: context.textTheme.bodySmall,
                      ),
                    ))
                .toList(),
            orElse: () => [],
          ),
        ),
      ),
    );
  }
}

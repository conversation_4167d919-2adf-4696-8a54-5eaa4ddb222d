import 'package:equatable/equatable.dart';
import 'package:zod/features/auction/data/models/auction.dart';
import 'package:zod/features/auction/data/models/auction_meta.dart';
import 'package:zod/features/auction/data/models/bid_history.dart';
import 'package:zod/features/auction/data/models/bid_history_meta.dart';
import 'package:zod/features/auth/auth.dart';

class AuctionState extends Equatable {
  // Auction details
  final bool isLoadingAuction;
  final bool isLoadingReminder;

  final String? auctionError;
  final Auction? auction;

  // Bid related state
  final bool isLoadingBids;
  final bool isLoadingMoreBids;
  final bool isPlacingBid;
  final bool bidSuccess;
  final int bidAmount;
  final String? bidError;
  final String? nextBidUrl;
  final BidHistoryMeta? bidMeta;
  final BidHistory? latestBid;
  final AuctionMeta? auctionMeta;
  final DateTime? breakUntil;

  const AuctionState({
    // Auction details
    required this.isLoadingAuction,
    this.auctionError,
    this.auction,
    this.breakUntil,
    this.isLoadingReminder = false,

    // Bid related state
    required this.isLoadingBids,
    required this.isLoadingMoreBids,
    required this.isPlacingBid,
    required this.bidSuccess,
    // required this.bids,
    this.bidError,
    this.bidAmount = 0,
    this.nextBidUrl,
    this.bidMeta,
    this.latestBid,
    this.auctionMeta,
  });

  factory AuctionState.initial() {
    return const AuctionState(
      isLoadingAuction: false,
      isLoadingBids: false,
      isLoadingMoreBids: false,
      isPlacingBid: false,
      bidSuccess: false,
      // bids: [],
    );
  }

  AuctionState copyWith({
    // Auction details
    bool? isLoadingAuction,
    bool? isWinner,
    String? auctionError,
    Auction? auction,
    DateTime? breakUntil,

    // Bid related state
    bool? isLoadingBids,
    bool? isLoadingMoreBids,
    bool? isPlacingBid,
    bool? bidSuccess,
    String? bidError,
    String? nextBidUrl,
    BidHistory? latestBid,
    AuctionMeta? auctionMeta,
    BidHistoryMeta? bidMeta,
    int? bidAmount,
    bool? isLoadingReminder,
  }) {
    return AuctionState(
      // Auction details
      isLoadingReminder: isLoadingReminder ?? this.isLoadingReminder,
      isLoadingAuction: isLoadingAuction ?? this.isLoadingAuction,
      auctionError: auctionError ?? this.auctionError,
      auction: auction ?? this.auction,
      breakUntil: breakUntil ?? this.breakUntil,

      // Bid related state
      isLoadingBids: isLoadingBids ?? this.isLoadingBids,
      isLoadingMoreBids: isLoadingMoreBids ?? this.isLoadingMoreBids,
      isPlacingBid: isPlacingBid ?? this.isPlacingBid,
      bidSuccess: bidSuccess ?? this.bidSuccess,
      bidError: bidError ?? this.bidError,
      nextBidUrl: nextBidUrl ?? this.nextBidUrl,
      latestBid: latestBid ?? this.latestBid,
      auctionMeta: auctionMeta ?? this.auctionMeta,
      bidAmount: bidAmount ?? this.bidAmount,
      bidMeta: bidMeta ?? this.bidMeta,
    );
  }


  @override
  List<Object?> get props => [
        // Auction details
        isLoadingAuction,
        auctionError,
        auction,

        // Bid related state
        isLoadingBids,
        isLoadingMoreBids,
        isPlacingBid,
        bidSuccess,
        bidError,
        nextBidUrl,
        bids,
        latestBid,
        auctionMeta,
        bidAmount,
        bidMeta,
        isLoadingReminder,
        breakUntil,
        auction?.isWinner,
        auction?.winnerId
      ];

  List<BidHistory> get bids => auction?.bids ?? [];

  // is my bid hieghts

  // is my bid is second
  bool get needReclaimNow =>  bids.isNotEmpty && bids.length >= 2 && bids[1].user?.id == Auth.user().id;

  //is  breakUntil  need to brack ...
  bool get needToBrack => isMyBidHeights &&
      breakUntil != null && breakUntil!.isAfter(DateTime.now());

// is my bid is heights
  bool get isMyBidHeights => bids.firstOrNull?.userId == Auth.user().id;
}

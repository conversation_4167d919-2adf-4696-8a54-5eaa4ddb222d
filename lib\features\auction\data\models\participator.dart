import 'package:bond_network/bond_network.dart';
import 'package:json_annotation/json_annotation.dart';

import '../../../auth/data/models/avatar.dart';

part 'participator.g.dart';

@JsonSerializable(explicitToJson: true)
class Participator extends Model {
  final String name;
  final Avatar? avatar;

  const Participator({
    required super.id,
    required this.name,
    this.avatar,
  });

  factory Participator.fromJson(Map<String, dynamic> json) =>
      _$ParticipatorFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$ParticipatorToJson(this);

  @override
  List<Object?> get props => [id];
}

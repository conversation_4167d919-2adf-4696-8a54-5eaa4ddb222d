import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/core/app_theme.dart';
import 'package:zod/core/resources/app_colors.dart';
import '../providers/timer_provider.dart';

class CountdownTimerView extends ConsumerWidget {
  final DateTime targetTime;
  final String title;
  final VoidCallback? onComplete;

  const CountdownTimerView({
    super.key,
    required this.targetTime,
    required this.title,
    this.onComplete,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final timerState = ref.watch(countdownTimerProvider(targetTime));

    // Parse the formatted time to check if it's completely ended
    final formattedTime = ref
        .read(countdownTimerProvider(targetTime).notifier)
        .formatDuration(timerState.remainingTime);
    final timeParts = formattedTime.split(':');
    final hours = int.tryParse(timeParts[0]) ?? 0;
    final minutes = int.tryParse(timeParts[1]) ?? 0;
    final seconds = int.tryParse(timeParts[2]) ?? 0;

    // Only hide the view when the timer has completely ended (00:00:00)
    // Keep it visible when there are no minutes but seconds remain (like 00:00:59)
    final shouldShow = !(hours == 0 && minutes == 0 && seconds == 0);

    return Visibility(
      visible: shouldShow,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: AppColors.mintCream,
          borderRadius: BorderRadius.circular(30),
          border: Border.all(color: AppColors.mintGreen),
        ),
        child: Text(
          "$title $formattedTime",
          style: context.textTheme.labelMedium?.deepGreen,
        ),
      ),
    );
  }
}

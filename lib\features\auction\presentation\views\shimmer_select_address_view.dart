import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import 'package:zod/core/resources/app_colors.dart';

class ShimmerSelectAddressView extends StatelessWidget {
  const ShimmerSelectAddressView({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title shimmer
          Shimmer.fromColors(
            baseColor: AppColors.platinum.withOpacity(0.4),
            highlightColor: AppColors.platinum.withOpacity(0.2),
            child: Container(
              width: 150,
              height: 20,
              decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),
          const SizedBox(height: 16),
          // Address list shimmer
          SizedBox(
            height: 200,
            child: Shimmer.fromColors(
              baseColor: AppColors.platinum.withOpacity(0.4),
              highlightColor: AppColors.platinum.withOpacity(0.2),
              child: ListView.separated(
                shrinkWrap: true,
                itemCount: 3, // Show 3 shimmer items
                separatorBuilder: (context, index) => const SizedBox(height: 12),
                itemBuilder: (context, index) => _buildAddressItemShimmer(),
              ),
            ),
          ),
          const SizedBox(height: 12),
          // Add address button shimmer
          Shimmer.fromColors(
            baseColor: AppColors.platinum.withOpacity(0.4),
            highlightColor: AppColors.platinum.withOpacity(0.2),
            child: Container(
              width: double.infinity,
              height: 50,
              decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: AppColors.softSteel),
              ),
            ),
          ),
          const SizedBox(height: 16),
          // Save changes button shimmer
          Shimmer.fromColors(
            baseColor: AppColors.platinum.withOpacity(0.4),
            highlightColor: AppColors.platinum.withOpacity(0.2),
            child: Container(
              width: double.infinity,
              height: 48,
              decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddressItemShimmer() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.white,
        border: Border.all(color: AppColors.lightGray),
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.all(16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Radio button shimmer
          Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              color: AppColors.white,
              shape: BoxShape.circle,
              border: Border.all(color: AppColors.lightGray),
            ),
          ),
          const SizedBox(width: 12),
          // Address text shimmer
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: double.infinity,
                  height: 16,
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  width: 200,
                  height: 14,
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:zod/features/auction/data/models/auction.dart';
import 'package:zod/features/auction/presentation/views/auction_item_view.dart';

import 'package:zod/core/resources/app_colors.dart';

class AuctionShimmer extends StatelessWidget {
  const AuctionShimmer({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Skeletonizer(
        enabled: true,
        containersColor: AppColors.lightGray,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
          child: Column(
            children: [
              AuctionItemView(auction: Auction.fake()),
            ],
          ),
        ),
      ),
    );
  }
}

// ignore_for_file: must_be_immutable

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/features/auth/data/models/phone_dto.dart';
import 'package:zod/features/auth/presentation/providers/allow_verify_provider.dart';

import '../../features/auth/presentation/providers/verification_otp_form_provider.dart';

class CountDownButtonView extends ConsumerStatefulWidget {
  CountDownButtonView({
    super.key,
    required this.title,
    required this.onPressed,
    this.initialCountdown,
    this.phoneDto,
    this.loading = false,
    this.enabled = true,
  });

  final String title;
  String? initialCountdown;
  final VoidCallback onPressed;
  final bool loading;
  final bool enabled;
  final PhoneDto? phoneDto;

  @override
  ConsumerState<CountDownButtonView> createState() =>
      _CountDownButtonViewState();
}

class _CountDownButtonViewState extends ConsumerState<CountDownButtonView> {
  late ValueNotifier<int> _remainingSeconds;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _remainingSeconds = ValueNotifier<int>(0);
  }

  clear() {
    widget.initialCountdown = null;
  }

  @override
  Widget build(BuildContext context) {
    int newSeconds = _extractSeconds(widget.initialCountdown ?? '');
    if (newSeconds == 0) {
      setState(() {
        _remainingSeconds.value = 0;
      });
    }
    if (_remainingSeconds.value != newSeconds && newSeconds > 0) {
      _remainingSeconds.value = newSeconds;
      _restartCountdown();
    }
    return ValueListenableBuilder<int>(
      valueListenable: _remainingSeconds,
      builder: (context, value, child) {
        return ElevatedButton(
          onPressed: widget.enabled && value == 0 ? widget.onPressed : null,
          style: ButtonStyle(
            side: WidgetStateProperty.resolveWith<BorderSide?>((states) {
              if (!widget.enabled) {
                return const BorderSide(
                    color: AppColors.lavenderMist, width: 1);
              }
              return null;
            }),
            shadowColor: WidgetStateProperty.resolveWith<Color?>((states) {
              if (!widget.enabled) {
                return AppColors.gunmetalGrayLight;
              }
              return null;
            }),
            elevation: WidgetStateProperty.resolveWith<double?>((states) {
              return widget.enabled ? null : 2;
            }),
          ),
          child: widget.loading
              ? const CircularProgressIndicator(
                  strokeWidth: 4,
                  color: AppColors.borderColor,
                  backgroundColor: AppColors.lighterGray,
                )
              : Text(
                  value > 0 ? _formatTime(value) : widget.title,
                ),
        );
      },
    );
  }

  int _extractSeconds(String timeString) {
    final RegExp regex = RegExp(r'(\d+):(\d+)');
    final Match? match = regex.firstMatch(timeString);

    if (match != null) {
      int minutes = int.parse(match.group(1)!);
      int seconds = int.parse(match.group(2)!);
      return (minutes * 60) + seconds;
    }
    return 0;
  }

  void _restartCountdown() {
    _timer?.cancel();
    _startCountdown();
  }

  void _startCountdown() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_remainingSeconds.value > 0) {
        _remainingSeconds.value--;
      } else {
        if (widget.phoneDto != null) {
          setState(() {
            _remainingSeconds.value = 0;
          });
          ref.invalidate(allowVerifyProvider(widget.phoneDto!));
          ref.invalidate(verificationCodeFormProvider(widget.phoneDto!));
          clear();
        }
        timer.cancel();
      }
    });
  }

  String _formatTime(int seconds) {
    final int minutes = seconds ~/ 60;
    final int remainingSeconds = seconds % 60;
    return '${_twoDigits(minutes)}:${_twoDigits(remainingSeconds)}';
  }

  String _twoDigits(int n) {
    return n.toString().padLeft(2, '0');
  }

  @override
  void dispose() {
    _timer?.cancel();
    _remainingSeconds.dispose();
    clear();
    super.dispose();
  }
}

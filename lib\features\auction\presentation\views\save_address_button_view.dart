import 'dart:developer';

import 'package:bond_form/bond_form.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/app/routes.dart';
import 'package:zod/core/widgets/app_button.dart';
import 'package:zod/features/auction/presentation/providers/add_address_form_provider.dart';
import 'package:zod/features/auction/presentation/providers/claim_prize_provider.dart';
import 'package:zod/features/auth/data/errors/validation_error.dart';
import 'package:zod/features/auth/presentation/providers/me_provider.dart';
import 'package:zod/features/home/<USER>/models/address.dart';

import '../../../auth/presentation/providers/phone_number_validation_provider.dart';

class SaveAddressButtonView extends ConsumerWidget {
  const SaveAddressButtonView({super.key, this.auctionId});

  final int? auctionId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formController = ref.watch(addAddressFormProvider.notifier);
    final formState = ref.watch(addAddressFormProvider);
    ref.listen(
      addAddressFormProvider,
      (previous, next) => _formStateListener(context, previous, next, ref),
    );
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: AppButton(
        title: 'Save Address',
        loading: formState.status == BondFormStateStatus.submitting,
        enabled: formState.status == BondFormStateStatus.valid,
        onPressed: formController.submit,
      ),
    );
  }

  void _formStateListener(
      BuildContext context,
      BondFormState<Address, ValidationError>? previous,
      BondFormState<Address, ValidationError> next,
      WidgetRef ref) async {
    switch (next.status) {
      case BondFormStateStatus.submitted:
        if (auctionId != null) {
          log("auction != null");
          ref
              .read(claimPrizeProvider(auctionId!).notifier)
              .selectAddress(next.success);
        }
        ref.invalidate(meProvider);
        goRouter.pop<Address>(next.success);
        break;
      default:
        break;
    }
  }
}

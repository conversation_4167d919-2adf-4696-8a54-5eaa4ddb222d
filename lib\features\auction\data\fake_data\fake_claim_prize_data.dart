import 'package:zod/features/auction/data/models/claim_prize.dart';
import 'package:zod/features/home/<USER>/models/address.dart';

/// Provides fake data for the Claim Prize feature
class FakeClaimPrizeData {
  /// Get a fake ClaimPrize object for testing
  static ClaimPrize getFakeClaimPrize(int auctionId) {
    // Create a fake claim prize with realistic data
    return ClaimPrize(
      id: auctionId,
      name: "iPhone 14 Pro Max - 256GB",
      image:
          "https://store.storeimages.cdn-apple.com/4982/as-images.apple.com/is/iphone-14-pro-finish-select-202209-6-7inch-deeppurple?wid=5120&hei=2880&fmt=p-jpg&qlt=80&.v=1663703841896",
      productCost: 4999,
      finalBidPoints: 2500,
      pointsValue: 2500,
      remainingToPay: 2499,
      taxPercentage: "15%",
      taxAmount: 375,
      totalToPay: 2874,
      claimBefore: DateTime.now().add(const Duration(hours: 24)),
    );
  }

  /// Get a list of fake addresses for testing
  static List<Address> getFakeAddresses() {
    return [
      Address(
        id: 1,
        userId: 1,
        cityId: 1,
        address: "Apartment 4B",
        houseNoStreet: "123 Main Street",
        phoneNumber: "+966501234567",
        latitude: "24.7136",
        longitude: "46.6753",
      ),
      Address(
        id: 2,
        userId: 1,
        cityId: 2,
        address: "Villa 7",
        houseNoStreet: "456 Palm Avenue",
        phoneNumber: "+966509876543",
        latitude: "21.4858",
        longitude: "39.1925",
      ),
    ];
  }

  /// Get a fake address by ID
  static Address? getFakeAddressById(int id) {
    final addresses = getFakeAddresses();
    try {
      return addresses.firstWhere((address) => address.id == id);
    } catch (e) {
      return null;
    }
  }
}

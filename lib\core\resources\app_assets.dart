import 'package:flutter/material.dart';

class AppImagesAssets {
  static const String logo = 'assets/images/app_logo.svg';
  static const String forceUpdate = 'assets/images/force_update.png';
  static const String _homeBondLight = 'assets/images/home_bond.png';
  static const String _homeBondDark = 'assets/images/home_bond_dark.png';

  static const String _homeBondArabicLight =
      'assets/images/home_bond_arabic.png';
  static const String _homeBondArabicDark =
      'assets/images/home_bond_arabic_dark.png';

  static String homeBond(Brightness mode, String locale) {
    if (mode == Brightness.light) {
      return locale == 'ar' ? _homeBondArabicLight : _homeBondLight;
    } else {
      return locale == 'ar' ? _homeBondArabicDark : _homeBondDark;
    }
  }

  static const String zod = 'assets/images/zod.svg';
  static const String backgroundPattern =
      'assets/images/background_pattern.png';
  static const String auctionCard = 'assets/images/auction_card.png';
  static const String walletItem = 'assets/images/wallet_item.png';
  static const String backgroundWallet = 'assets/images/background.png';
  static const String backgroundPattern2 =
      'assets/images/backgroundPattern2.png';
  static const String homePattern = 'assets/images/home.png';
  static const String confirmJoin = 'assets/images/confirm_join.png';
  static const String auctionCompetition = 'assets/images/competetion_back.png';
  static const String splashBackground = 'assets/images/splash_bck.svg';
  static const String splashPart= 'assets/images/splash_part.svg';
}

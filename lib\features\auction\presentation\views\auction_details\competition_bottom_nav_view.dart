import 'dart:io';

import 'package:bond_core/bond_core.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:zod/app/routes.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/app_theme.dart';
import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/core/resources/app_icons.dart';
import 'package:zod/core/widgets/app_button.dart';
import 'package:zod/features/auction/data/models/auction.dart';
import 'package:zod/features/auction/presentation/views/auction_details/reminder_button_view.dart';

import 'package:zod/features/auth/auth.dart';

import '../../join_to_auction.dart';

class CompetitionBottomNavView extends ConsumerWidget {
  const CompetitionBottomNavView({required this.auction, super.key});

  final Auction auction;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (!(auction.isJoined ?? false))
          Container(
              padding: EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              decoration: BoxDecoration(
                color: AppColors.softWhite,
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.info_outline,
                      size: 20, color: AppColors.primaryColor),
                  SizedBox(width: 4),
                  Text(context.localizations.small_participations_fees,
                      style: context.textTheme.labelMedium),
                  Text(" =", style: context.textTheme.labelMedium),
                  SizedBox(width: 8),
                  Row(
                    children: [
                      SvgPicture.asset(AppIcons.coin),
                      SizedBox(width: 4),
                      Text(auction.entryFee?.toString() ?? "-",
                          style: context.textTheme.labelMedium?.royalBlue.w700),
                    ],
                  ),
                ],
              )),
        Container(
          padding: EdgeInsets.only(left: 16, right: 16, top: 12, bottom: 10),
          decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.only(
                topRight: Radius.circular(10),
                topLeft: Radius.circular(10),
              ),
              boxShadow: [
                BoxShadow(
                  blurRadius: 8,
                  offset: Offset(0, -2),
                  color: AppColors.blackShadow,
                ),
              ]),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Row(
                children: [
                  SizedBox(width: 15),
                  Align(
                    widthFactor: 0.8,
                    child: Row(
                      children: [
                        ...(auction.participatorsLimited)
                            .asMap()
                            .entries
                            .map((entry) {
                          return Align(
                            widthFactor: 0.6,
                            // parent circle avatar.
                            // We defined this for better UI
                            child: CircleAvatar(
                              radius: 32 / 2,
                              backgroundColor: Colors.white,
                              // Child circle avatar
                              child: CircleAvatar(
                                radius: 30 / 2,
                                backgroundImage: CachedNetworkImageProvider(
                                    entry.value.avatar?.url ?? ""),
                                // child: NetworkImageView(
                                //   entry.value.avatar?.url ?? "",
                                //   width: 20,
                                //   height: 20,
                                //   borderColor: AppColors.white,
                                //   radius: 11.21,
                                // ),
                                // backgroundImage: NetworkImage(entry.value?.avatar?.url),
                              ),
                            ),
                          );
                        }),
                        Align(
                            widthFactor:
                                auction.participatorsLimited.isEmpty ? 0 : 0.5,
                            // parent circle avatar.
                            // We defined this for better UI
                            child: SvgPicture.asset(
                              auction.isAllSets
                                  ? AppIcons.correct
                                  : AppIcons.usersStacked,
                              width: 35,
                              height: 35,
                            ))
                      ],
                    ),
                  ),
                  SizedBox(width: 18),
                  Expanded(
                    child: Text(
                      auction.massegestatus(context),
                      style: context.textTheme.bodyMedium!.medium,
                    ),
                  ),
                  SizedBox(width: 8),

                  /// here add reminder button
                  ReminderButtonView(auction: auction),
                  SizedBox(width: 5),
                ],
              ),
              SizedBox(
                height: 8,
              ),
              SizedBox(
                height: 10,
                child: ClipRRect(
                  borderRadius: BorderRadius.all(Radius.circular(10)),
                  child: LinearProgressIndicator(
                    value: auction.participatorsProgress,
                    valueColor: AlwaysStoppedAnimation<Color>(
                        AppColors.progressBarColor),
                    backgroundColor: AppColors.softSilver,
                  ),
                ),
              ),
              SizedBox(
                height: 8,
              ),

              Visibility(
                visible: auction.isJoined ?? false,
                child: Column(
                  children: [
                    Container(
                      padding: EdgeInsets.all(8),
                      margin: EdgeInsets.symmetric(vertical: 8),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        border: Border.all(
                          color: AppColors.softSilver,
                          width: 1,
                        ),
                      ),
                      child: Row(
                        children: [
                          Text(context.localizations.starting_bid,
                              style: context.textTheme.labelLarge!),
                          Spacer(),
                          Row(
                            children: [
                              SvgPicture.asset(AppIcons.coin),
                              Text(" ${auction.openBidPoints}",
                                  style: context.textTheme.labelLarge!),
                            ],
                          )
                        ],
                      ),
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(context.localizations.you_have,
                            style: context.textTheme.bodySmall),
                        SvgPicture.asset(AppIcons.coin),
                        Text(
                            " ${Auth.check() ? Auth.user().availablePoint : 0}",
                            style: context.textTheme.bodySmall?.w700),
                      ],
                    )
                  ],
                ),
              ),

              Visibility(
                visible: auction.isFullySubscribed && auction.isNotJoined,
                child: AppButton(
                  title: context.localizations.exploreWhatComing,
                  textColor: AppColors.steelGray,
                  backgroundColor: AppColors.disabled,
                  borderColor: AppColors.softSteel,
                  onPressed: () {
                    goRouter.go('/home');
                  },
                ),
                replacement: Visibility(
                  visible: !auction.isJoined!,
                  child: AppButton(
                    title: context.localizations.claim_your_seat,
                    onPressed: () {
                      // goRouter.state.path
                      if (Auth.check()) {
                        if (Auth.user().availablePoint >= auction.entryFee!) {
                          goRouter.push(JoinToAuctionView.route,
                              extra: auction);
                        } else {
                          var url =
                              "/charge_wallet/${auction.id}/${auction.entryFee}/${auction.incrementBidPoints}";
                          goRouter.push(url, extra: auction);
                        }
                      } else {
                        goRouterBackToAfterLogin = '/auction/${auction.id}';
                        goRouter.push('/login');
                      }
                    },
                    enabled: auction.isFullySubscribed == false,
                  ),
                ),
              ),

              if (Platform.isIOS) SizedBox(height: 8),
              // Text(
              //   context.localizations.afew_seats_left,
              //   style: context.textTheme.bodySmall,
              // ),
            ],
          ),
        ),
      ],
    );
  }
}

import 'dart:developer';

import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/app/routes.dart';
import 'package:zod/core/notifications/notification_alert_view.dart';
import 'package:zod/features/auction/data/api.dart';
import 'package:zod/features/auction/data/events/auction_signup_event_tracker.dart';
import 'package:zod/features/auction/data/models/auction.dart';
import 'package:zod/features/auction/presentation/providers/auction_provider.dart';
import 'package:zod/features/auth/presentation/providers/me_provider.dart';

import '../../data/states/join_auction_state.dart';

final joinAuctionProvider = StateNotifierProvider.family<JoinAuctionProvider,
    JoinAuctionState, Auction>(
  (ref, auction) {
    return JoinAuctionProvider(
      api: sl<AuctionApi>(),
      auction: auction,
    );
  },
);

class JoinAuctionProvider extends StateNotifier<JoinAuctionState> {
  final AuctionApi api;
  final Auction auction;

  JoinAuctionProvider({required this.api, required this.auction})
      : super(JoinAuctionState(
          isLoading: false,
        )) {}

  void joinToAuction(ref) async {
    state = state.copyWith(isLoading: true);
    try {
      final chargeUser = await api.join(auction.id);
      if (chargeUser.meta.success ?? false) {
        goRouter.pop();
        NotificationAlert.showLocalNotification(
          chargeUser.meta.message ?? "",
          type: ToastType.success,
          align: Alignment.topCenter,
        );

        // Track the auction signup event
        trackAuctionSignup(auction);
        log('User joined auction ${auction.id}', name: 'JoinAuctionProvider');

        ref.read(auctionProvider(auction.id).notifier).refreshAuction();
        ref.invalidate(meProvider);
      } else {
        NotificationAlert.showLocalNotification(
          chargeUser.meta.message ?? "",
          type: ToastType.error,
        );
      }
    } catch (e) {
      // ref.read(auctionProvider(auction.id).notifier).refreshAuction();
      goRouter.pop();
      NotificationAlert.showLocalNotification(e.toString(),
          type: ToastType.error);
    } finally {
      state = state.copyWith(isLoading: false);
    }
  }
}

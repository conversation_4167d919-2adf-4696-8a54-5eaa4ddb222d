import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:bond_core/bond_core.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/resources/app_colors.dart';

class AuctionTimesUp extends StatelessWidget {
  const AuctionTimesUp({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 46, horizontal: 28),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppColors.coralPink),
        gradient: const LinearGradient(
          begin: Alignment.topRight,
          end: Alignment.bottomLeft,
          colors: [
            Color.fromRGBO(250, 245, 245, 0.11),
            AppColors.coralPink,
          ],
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SvgPicture.asset(
            'assets/images/wall_clock.svg',
            width: 48,
            height: 48,
          ),
          const SizedBox(height: 8),
          Text(
            context.localizations.times_up,
            style: context.textTheme.titleLarge?.copyWith(
              color: AppColors.textColor,
              fontWeight: FontWeight.w700,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            context.localizations.prize_no_longer_available,
            style: context.textTheme.labelMedium?.copyWith(
              color: AppColors.slateGray,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

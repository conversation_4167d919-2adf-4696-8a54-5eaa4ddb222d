import 'package:flutter/material.dart';
import 'package:zod/core/resources/app_icons.dart';
import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/features/auction/data/models/auction.dart';

import 'html_section_group_view.dart';

class AboutAuctionView extends StatelessWidget {
  const AboutAuctionView({super.key, required this.auction});

  final Auction auction;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      color: AppColors.white,
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            HtmlSectionGroupView(
              html: auction.description ?? '',
              svgIcons: [
                AppIcons.info,
                AppIcons.categories,
              ],
              showDividerAfter: auction.technicalSpecifications != null,
            ),
            HtmlSectionGroupView(
              html: auction.technicalSpecifications ?? '',
              svgIcons: [
                AppIcons.categories,
                AppIcons.info,
              ],
            ),
          ],
        ),
      ),
    );
  }
}

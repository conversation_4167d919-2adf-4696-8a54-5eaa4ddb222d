import 'dart:developer';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:zod/core/app_theme.dart';
import 'package:zod/core/resources/app_colors.dart';

// Custom clipper for triangle shape
class TriangleClipper extends CustomClipper<Path> {
  @override
  Path getClip(ui.Size size) {
    final path = Path();
    path.moveTo(size.width / 2, size.height); // Bottom center point
    path.lineTo(0, 0); // Top left corner
    path.lineTo(size.width, 0); // Top right corner
    path.close();
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}

class CustomInfoWindowView extends StatelessWidget {
  final String address;

  const CustomInfoWindowView({super.key, required this.address});

  @override
  Widget build(BuildContext context) {
    log("TTTTT address ${address}");
    // Use a SizedBox to constrain the overall height
    return SizedBox(
      height: 55, // Total height: 45 (content) + 10 (triangle)
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Main content container
          Container(
            height: 45, // Increased height for better visibility
            width: 250, // Match the width of the CustomInfoWindow
            decoration: BoxDecoration(
              color: AppColors.darkSteel,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    child: Text(
                      // Use the address parameter or a default message
                      address.isNotEmpty ? address : 'Selected Location',
                      // Add a key to force rebuild when the address changes
                      key: ValueKey(address),
                      style: Theme.of(context).textTheme.labelMedium?.white,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          ),
          // Triangle pointing down using ClipPath
          ClipPath(
            clipper: TriangleClipper(),
            child: Container(
              width: 16,
              height: 8,
              color: AppColors.darkSteel,
            ),
          ),
        ],
      ),
    );
  }
}

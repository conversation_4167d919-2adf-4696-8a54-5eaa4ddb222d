[2025-05-15 14:41:03] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#9 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#10 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('Illuminate\\\\Cook...')
#13 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('Illuminate\\\\Cook...', Array, true)
#14 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Cook...', Array)
#15 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('Illuminate\\\\Cook...', Array)
#16 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(197): Illuminate\\Foundation\\Application->make('Illuminate\\\\Cook...')
#17 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#46 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\D...')
#50 {main}
"} 
[2025-05-15 14:41:05] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#9 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#10 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('Illuminate\\\\Cook...')
#13 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('Illuminate\\\\Cook...', Array, true)
#14 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Cook...', Array)
#15 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('Illuminate\\\\Cook...', Array)
#16 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(257): Illuminate\\Foundation\\Application->make('Illuminate\\\\Cook...')
#17 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(215): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1221): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#19 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\D...')
#21 {main}
"} 
[2025-05-15 14:41:07] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#9 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#10 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('Illuminate\\\\Cook...')
#13 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('Illuminate\\\\Cook...', Array, true)
#14 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Cook...', Array)
#15 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('Illuminate\\\\Cook...', Array)
#16 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(197): Illuminate\\Foundation\\Application->make('Illuminate\\\\Cook...')
#17 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#46 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\D...')
#50 {main}
"} 
[2025-05-15 14:41:07] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#9 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#10 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('Illuminate\\\\Cook...')
#13 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('Illuminate\\\\Cook...', Array, true)
#14 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Cook...', Array)
#15 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('Illuminate\\\\Cook...', Array)
#16 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(257): Illuminate\\Foundation\\Application->make('Illuminate\\\\Cook...')
#17 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(215): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1221): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#19 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\D...')
#21 {main}
"} 
[2025-05-15 14:41:08] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#9 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#10 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('Illuminate\\\\Cook...')
#13 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('Illuminate\\\\Cook...', Array, true)
#14 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Cook...', Array)
#15 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('Illuminate\\\\Cook...', Array)
#16 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(197): Illuminate\\Foundation\\Application->make('Illuminate\\\\Cook...')
#17 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\filament\\filament\\src\\Http\\Middleware\\SetUpPanel.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Filament\\Http\\Middleware\\SetUpPanel->handle(Object(Illuminate\\Http\\Request), Object(Closure), Object(Filament\\Panel))
#19 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#21 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\D...')
#52 {main}
"} 
[2025-05-15 14:41:08] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#9 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#10 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('Illuminate\\\\Cook...')
#13 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('Illuminate\\\\Cook...', Array, true)
#14 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Cook...', Array)
#15 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('Illuminate\\\\Cook...', Array)
#16 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(257): Illuminate\\Foundation\\Application->make('Illuminate\\\\Cook...')
#17 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(215): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1221): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#19 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\D...')
#21 {main}
"} 
[2025-05-15 14:44:01] production.ERROR: Class "Filament\Actions\Exports\ExportAction" not found {"exception":"[object] (Error(code: 0): Class \"Filament\\Actions\\Exports\\ExportAction\" not found at C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Providers\\FilamentExportServiceProvider.php:24)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\FilamentExportServiceProvider->boot()
#1 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#4 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#5 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1150): Illuminate\\Container\\Container->call(Array)
#6 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\FilamentExportServiceProvider))
#7 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\FilamentExportServiceProvider), 'App\\\\Providers\\\\F...')
#8 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk(Array, Object(Closure))
#9 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#10 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#11 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#12 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#13 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\D...')
#17 {main}
"} 
[2025-05-15 14:44:02] production.ERROR: SQLSTATE[HY000] [1049] Unknown database 'laravel' (Connection: mysql, SQL: select count(*) as aggregate from `telescope_entries` where `type` = exception and `family_hash` = 292bb0e8cd61839a7642bde5950eedca) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1049): SQLSTATE[HY000] [1049] Unknown database 'laravel' (Connection: mysql, SQL: select count(*) as aggregate from `telescope_entries` where `type` = exception and `family_hash` = 292bb0e8cd61839a7642bde5950eedca) at C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3107): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#3 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3092): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3679): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3605): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3533): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#8 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(123): Illuminate\\Database\\Query\\Builder->count()
#9 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(165): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->countExceptionOccurences(Object(Laravel\\Telescope\\IncomingExceptionEntry))
#10 [internal function]: Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Laravel\\Telescope\\IncomingExceptionEntry), 0)
#11 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(609): array_map(Object(Closure), Array, Array)
#12 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(800): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#13 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(164): Illuminate\\Support\\Collection->map(Object(Closure))
#14 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(274): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#15 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(163): Illuminate\\Support\\Collection->each(Object(Closure))
#16 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(140): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->storeExceptions(Object(Illuminate\\Support\\Collection))
#17 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#18 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#19 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Telescope.php(286): call_user_func(Object(Closure))
#20 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#21 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#22 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#23 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#27 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(217): Illuminate\\Foundation\\Application->terminate()
#29 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1221): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\D...')
#32 {main}

[previous exception] [object] (PDOException(code: 1049): SQLSTATE[HY000] [1049] Unknown database 'laravel' at C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:66)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(66): PDO->__construct('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#1 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#2 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1228): call_user_func(Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1264): Illuminate\\Database\\Connection->getPdo()
#7 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(509): Illuminate\\Database\\Connection->getReadPdo()
#8 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select count(*)...', Array)
#10 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#11 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#12 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3107): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#13 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3092): Illuminate\\Database\\Query\\Builder->runSelect()
#14 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3679): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3605): Illuminate\\Database\\Query\\Builder->get(Array)
#17 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3533): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#18 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(123): Illuminate\\Database\\Query\\Builder->count()
#19 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(165): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->countExceptionOccurences(Object(Laravel\\Telescope\\IncomingExceptionEntry))
#20 [internal function]: Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Laravel\\Telescope\\IncomingExceptionEntry), 0)
#21 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(609): array_map(Object(Closure), Array, Array)
#22 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(800): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(164): Illuminate\\Support\\Collection->map(Object(Closure))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(274): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#25 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(163): Illuminate\\Support\\Collection->each(Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(140): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->storeExceptions(Object(Illuminate\\Support\\Collection))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#28 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#29 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Telescope.php(286): call_user_func(Object(Closure))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#33 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#34 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#37 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(217): Illuminate\\Foundation\\Application->terminate()
#39 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1221): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\D...')
#42 {main}
"} 
[2025-05-15 14:44:18] production.ERROR: Class "Filament\Actions\Exports\ExportAction" not found {"exception":"[object] (Error(code: 0): Class \"Filament\\Actions\\Exports\\ExportAction\" not found at C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Providers\\FilamentExportServiceProvider.php:24)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\FilamentExportServiceProvider->boot()
#1 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#4 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#5 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1150): Illuminate\\Container\\Container->call(Array)
#6 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\FilamentExportServiceProvider))
#7 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\FilamentExportServiceProvider), 'App\\\\Providers\\\\F...')
#8 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk(Array, Object(Closure))
#9 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#10 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#11 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#12 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#13 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\D...')
#17 {main}
"} 
[2025-05-15 14:44:18] production.ERROR: SQLSTATE[HY000] [1049] Unknown database 'laravel' (Connection: mysql, SQL: select count(*) as aggregate from `telescope_entries` where `type` = exception and `family_hash` = 292bb0e8cd61839a7642bde5950eedca) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1049): SQLSTATE[HY000] [1049] Unknown database 'laravel' (Connection: mysql, SQL: select count(*) as aggregate from `telescope_entries` where `type` = exception and `family_hash` = 292bb0e8cd61839a7642bde5950eedca) at C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3107): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#3 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3092): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3679): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3605): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3533): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#8 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(123): Illuminate\\Database\\Query\\Builder->count()
#9 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(165): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->countExceptionOccurences(Object(Laravel\\Telescope\\IncomingExceptionEntry))
#10 [internal function]: Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Laravel\\Telescope\\IncomingExceptionEntry), 0)
#11 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(609): array_map(Object(Closure), Array, Array)
#12 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(800): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#13 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(164): Illuminate\\Support\\Collection->map(Object(Closure))
#14 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(274): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#15 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(163): Illuminate\\Support\\Collection->each(Object(Closure))
#16 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(140): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->storeExceptions(Object(Illuminate\\Support\\Collection))
#17 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#18 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#19 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Telescope.php(286): call_user_func(Object(Closure))
#20 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#21 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#22 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#23 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#27 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(217): Illuminate\\Foundation\\Application->terminate()
#29 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1221): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\D...')
#32 {main}

[previous exception] [object] (PDOException(code: 1049): SQLSTATE[HY000] [1049] Unknown database 'laravel' at C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:66)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(66): PDO->__construct('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#1 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#2 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1228): call_user_func(Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1264): Illuminate\\Database\\Connection->getPdo()
#7 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(509): Illuminate\\Database\\Connection->getReadPdo()
#8 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select count(*)...', Array)
#10 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#11 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#12 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3107): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#13 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3092): Illuminate\\Database\\Query\\Builder->runSelect()
#14 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3679): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3605): Illuminate\\Database\\Query\\Builder->get(Array)
#17 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3533): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#18 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(123): Illuminate\\Database\\Query\\Builder->count()
#19 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(165): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->countExceptionOccurences(Object(Laravel\\Telescope\\IncomingExceptionEntry))
#20 [internal function]: Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Laravel\\Telescope\\IncomingExceptionEntry), 0)
#21 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(609): array_map(Object(Closure), Array, Array)
#22 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(800): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(164): Illuminate\\Support\\Collection->map(Object(Closure))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(274): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#25 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(163): Illuminate\\Support\\Collection->each(Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(140): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->storeExceptions(Object(Illuminate\\Support\\Collection))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#28 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#29 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Telescope.php(286): call_user_func(Object(Closure))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#33 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#34 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#37 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(217): Illuminate\\Foundation\\Application->terminate()
#39 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1221): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\D...')
#42 {main}
"} 
[2025-05-15 14:44:24] production.ERROR: Class "Filament\Actions\Exports\ExportAction" not found {"exception":"[object] (Error(code: 0): Class \"Filament\\Actions\\Exports\\ExportAction\" not found at C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Providers\\FilamentExportServiceProvider.php:24)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\FilamentExportServiceProvider->boot()
#1 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#4 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#5 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1150): Illuminate\\Container\\Container->call(Array)
#6 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\FilamentExportServiceProvider))
#7 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\FilamentExportServiceProvider), 'App\\\\Providers\\\\F...')
#8 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk(Array, Object(Closure))
#9 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#10 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#11 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#12 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#13 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\D...')
#17 {main}
"} 
[2025-05-15 14:44:24] production.ERROR: SQLSTATE[HY000] [1049] Unknown database 'laravel' (Connection: mysql, SQL: select count(*) as aggregate from `telescope_entries` where `type` = exception and `family_hash` = 292bb0e8cd61839a7642bde5950eedca) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1049): SQLSTATE[HY000] [1049] Unknown database 'laravel' (Connection: mysql, SQL: select count(*) as aggregate from `telescope_entries` where `type` = exception and `family_hash` = 292bb0e8cd61839a7642bde5950eedca) at C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3107): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#3 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3092): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3679): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3605): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3533): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#8 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(123): Illuminate\\Database\\Query\\Builder->count()
#9 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(165): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->countExceptionOccurences(Object(Laravel\\Telescope\\IncomingExceptionEntry))
#10 [internal function]: Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Laravel\\Telescope\\IncomingExceptionEntry), 0)
#11 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(609): array_map(Object(Closure), Array, Array)
#12 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(800): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#13 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(164): Illuminate\\Support\\Collection->map(Object(Closure))
#14 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(274): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#15 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(163): Illuminate\\Support\\Collection->each(Object(Closure))
#16 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(140): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->storeExceptions(Object(Illuminate\\Support\\Collection))
#17 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#18 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#19 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Telescope.php(286): call_user_func(Object(Closure))
#20 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#21 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#22 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#23 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#27 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(217): Illuminate\\Foundation\\Application->terminate()
#29 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1221): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\D...')
#32 {main}

[previous exception] [object] (PDOException(code: 1049): SQLSTATE[HY000] [1049] Unknown database 'laravel' at C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:66)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(66): PDO->__construct('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#1 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#2 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1228): call_user_func(Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1264): Illuminate\\Database\\Connection->getPdo()
#7 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(509): Illuminate\\Database\\Connection->getReadPdo()
#8 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select count(*)...', Array)
#10 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#11 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#12 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3107): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#13 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3092): Illuminate\\Database\\Query\\Builder->runSelect()
#14 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3679): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3605): Illuminate\\Database\\Query\\Builder->get(Array)
#17 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3533): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#18 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(123): Illuminate\\Database\\Query\\Builder->count()
#19 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(165): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->countExceptionOccurences(Object(Laravel\\Telescope\\IncomingExceptionEntry))
#20 [internal function]: Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Laravel\\Telescope\\IncomingExceptionEntry), 0)
#21 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(609): array_map(Object(Closure), Array, Array)
#22 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(800): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(164): Illuminate\\Support\\Collection->map(Object(Closure))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(274): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#25 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(163): Illuminate\\Support\\Collection->each(Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(140): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->storeExceptions(Object(Illuminate\\Support\\Collection))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#28 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#29 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Telescope.php(286): call_user_func(Object(Closure))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#33 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#34 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#37 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(217): Illuminate\\Foundation\\Application->terminate()
#39 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1221): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\D...')
#42 {main}
"} 
[2025-05-15 14:52:51] local.ERROR: Class "Filament\Actions\Exports\ExportAction" not found {"exception":"[object] (Error(code: 0): Class \"Filament\\Actions\\Exports\\ExportAction\" not found at C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Providers\\FilamentExportServiceProvider.php:24)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\FilamentExportServiceProvider->boot()
#1 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#4 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#5 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1150): Illuminate\\Container\\Container->call(Array)
#6 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\FilamentExportServiceProvider))
#7 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\FilamentExportServiceProvider), 'App\\\\Providers\\\\F...')
#8 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk(Array, Object(Closure))
#9 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#10 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#11 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#12 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#13 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\D...')
#17 {main}
"} 
[2025-05-15 14:53:02] local.ERROR: SQLSTATE[HY000] [1049] Unknown database 'rydo_backend' (Connection: mysql, SQL: select count(*) as aggregate from `telescope_entries` where `type` = exception and `family_hash` = 292bb0e8cd61839a7642bde5950eedca) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1049): SQLSTATE[HY000] [1049] Unknown database 'rydo_backend' (Connection: mysql, SQL: select count(*) as aggregate from `telescope_entries` where `type` = exception and `family_hash` = 292bb0e8cd61839a7642bde5950eedca) at C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3107): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#3 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3092): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3679): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3605): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3533): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#8 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(123): Illuminate\\Database\\Query\\Builder->count()
#9 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(165): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->countExceptionOccurences(Object(Laravel\\Telescope\\IncomingExceptionEntry))
#10 [internal function]: Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Laravel\\Telescope\\IncomingExceptionEntry), 0)
#11 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(609): array_map(Object(Closure), Array, Array)
#12 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(800): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#13 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(164): Illuminate\\Support\\Collection->map(Object(Closure))
#14 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(274): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#15 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(163): Illuminate\\Support\\Collection->each(Object(Closure))
#16 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(140): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->storeExceptions(Object(Illuminate\\Support\\Collection))
#17 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#18 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#19 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Telescope.php(286): call_user_func(Object(Closure))
#20 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#21 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#22 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#23 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#27 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(217): Illuminate\\Foundation\\Application->terminate()
#29 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1221): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\D...')
#32 {main}

[previous exception] [object] (PDOException(code: 1049): SQLSTATE[HY000] [1049] Unknown database 'rydo_backend' at C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:66)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(66): PDO->__construct('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#1 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#2 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1228): call_user_func(Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1264): Illuminate\\Database\\Connection->getPdo()
#7 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(509): Illuminate\\Database\\Connection->getReadPdo()
#8 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select count(*)...', Array)
#10 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#11 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#12 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3107): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#13 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3092): Illuminate\\Database\\Query\\Builder->runSelect()
#14 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3679): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3605): Illuminate\\Database\\Query\\Builder->get(Array)
#17 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3533): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#18 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(123): Illuminate\\Database\\Query\\Builder->count()
#19 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(165): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->countExceptionOccurences(Object(Laravel\\Telescope\\IncomingExceptionEntry))
#20 [internal function]: Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Laravel\\Telescope\\IncomingExceptionEntry), 0)
#21 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(609): array_map(Object(Closure), Array, Array)
#22 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(800): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(164): Illuminate\\Support\\Collection->map(Object(Closure))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(274): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#25 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(163): Illuminate\\Support\\Collection->each(Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(140): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->storeExceptions(Object(Illuminate\\Support\\Collection))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#28 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#29 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Telescope.php(286): call_user_func(Object(Closure))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#33 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#34 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#37 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(217): Illuminate\\Foundation\\Application->terminate()
#39 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1221): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\D...')
#42 {main}
"} 
[2025-05-15 14:57:47] local.ERROR: Method Filament\Actions\ExportAction::registerExporter does not exist. {"exception":"[object] (BadMethodCallException(code: 0): Method Filament\\Actions\\ExportAction::registerExporter does not exist. at C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\filament\\support\\src\\Concerns\\Macroable.php:50)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Providers\\FilamentExportServiceProvider.php(24): Filament\\Support\\Components\\Component::__callStatic('registerExporte...', Array)
#1 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\FilamentExportServiceProvider->boot()
#2 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#5 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#6 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1150): Illuminate\\Container\\Container->call(Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\FilamentExportServiceProvider))
#8 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\FilamentExportServiceProvider), 'App\\\\Providers\\\\F...')
#9 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk(Array, Object(Closure))
#10 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#11 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#12 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#13 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#14 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\D...')
#18 {main}
"} 
[2025-05-15 14:57:51] local.ERROR: SQLSTATE[HY000] [1049] Unknown database 'rydo_backend' (Connection: mysql, SQL: select count(*) as aggregate from `telescope_entries` where `type` = exception and `family_hash` = 0470e2f4dc2fb180d0ccbd00df2c9c7b) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1049): SQLSTATE[HY000] [1049] Unknown database 'rydo_backend' (Connection: mysql, SQL: select count(*) as aggregate from `telescope_entries` where `type` = exception and `family_hash` = 0470e2f4dc2fb180d0ccbd00df2c9c7b) at C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3107): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#3 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3092): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3679): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3605): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3533): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#8 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(123): Illuminate\\Database\\Query\\Builder->count()
#9 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(165): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->countExceptionOccurences(Object(Laravel\\Telescope\\IncomingExceptionEntry))
#10 [internal function]: Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Laravel\\Telescope\\IncomingExceptionEntry), 0)
#11 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(609): array_map(Object(Closure), Array, Array)
#12 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(800): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#13 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(164): Illuminate\\Support\\Collection->map(Object(Closure))
#14 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(274): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#15 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(163): Illuminate\\Support\\Collection->each(Object(Closure))
#16 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(140): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->storeExceptions(Object(Illuminate\\Support\\Collection))
#17 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#18 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#19 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Telescope.php(286): call_user_func(Object(Closure))
#20 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#21 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#22 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#23 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#27 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(217): Illuminate\\Foundation\\Application->terminate()
#29 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1221): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\D...')
#32 {main}

[previous exception] [object] (PDOException(code: 1049): SQLSTATE[HY000] [1049] Unknown database 'rydo_backend' at C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:66)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(66): PDO->__construct('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#1 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#2 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1228): call_user_func(Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1264): Illuminate\\Database\\Connection->getPdo()
#7 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(509): Illuminate\\Database\\Connection->getReadPdo()
#8 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select count(*)...', Array)
#10 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#11 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#12 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3107): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#13 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3092): Illuminate\\Database\\Query\\Builder->runSelect()
#14 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3679): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3605): Illuminate\\Database\\Query\\Builder->get(Array)
#17 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3533): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#18 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(123): Illuminate\\Database\\Query\\Builder->count()
#19 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(165): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->countExceptionOccurences(Object(Laravel\\Telescope\\IncomingExceptionEntry))
#20 [internal function]: Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Laravel\\Telescope\\IncomingExceptionEntry), 0)
#21 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(609): array_map(Object(Closure), Array, Array)
#22 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(800): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(164): Illuminate\\Support\\Collection->map(Object(Closure))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(274): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#25 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(163): Illuminate\\Support\\Collection->each(Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(140): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->storeExceptions(Object(Illuminate\\Support\\Collection))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#28 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#29 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Telescope.php(286): call_user_func(Object(Closure))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#33 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#34 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#37 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(217): Illuminate\\Foundation\\Application->terminate()
#39 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1221): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\D...')
#42 {main}
"} 
[2025-05-28 14:16:19] local.ERROR: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (9f04bf3b-ccdb-4981-9888-b9162d85fded, {"command":"make:migration","exit_code":0,"arguments":{"command":"make:migration","name":"add_soft_deletes_to_user_related_tables"},"options":{"create":null,"table":null,"path":null,"realpath":false,"fullpath":false,"help":false,"silent":false,"quiet":false,"verbose":false,"version":false,"ansi":null,"no-interaction":false,"env":null},"hostname":"Ahmed"}, 2025-05-28 14:16:10, ?, command, 9f04bf3b-96ed-43e7-8d2e-db2d51421830)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (9f04bf3b-ccdb-4981-9888-b9162d85fded, {\"command\":\"make:migration\",\"exit_code\":0,\"arguments\":{\"command\":\"make:migration\",\"name\":\"add_soft_deletes_to_user_related_tables\"},\"options\":{\"create\":null,\"table\":null,\"path\":null,\"realpath\":false,\"fullpath\":false,\"help\":false,\"silent\":false,\"quiet\":false,\"verbose\":false,\"version\":false,\"ansi\":null,\"no-interaction\":false,\"env\":null},\"hostname\":\"Ahmed\"}, 2025-05-28 14:16:10, ?, command, 9f04bf3b-96ed-43e7-8d2e-db2d51421830)) at C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(976): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(955): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'insert into `te...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'insert into `te...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3720): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#5 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#6 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(274): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#8 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#9 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#10 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Telescope.php(286): call_user_func(Object(Closure))
#11 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#12 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#13 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#14 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#17 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#18 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#19 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#20 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#21 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#22 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it at C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:66)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(66): PDO->__construct('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#1 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(85): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#2 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection(Object(PDOException), 'mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#3 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#4 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1228): call_user_func(Object(Closure))
#7 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(47): Illuminate\\Database\\Connection->getPdo()
#8 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}('insert into `te...', Array)
#9 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(976): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#10 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(955): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'insert into `te...', Array, Object(Closure))
#11 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'insert into `te...', Array, Object(Closure))
#12 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#13 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3720): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#14 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#15 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(274): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#16 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#17 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#18 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#19 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Telescope.php(286): call_user_func(Object(Closure))
#20 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#21 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#22 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#23 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#27 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#29 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#30 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#31 {main}
"} 
[2025-05-28 14:17:29] local.ERROR: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select exists (select 1 from information_schema.tables where table_schema = schema() and table_name = 'migrations' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select exists (select 1 from information_schema.tables where table_schema = schema() and table_name = 'migrations' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`) at C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(976): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(955): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(341): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#5 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(358): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#6 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(174): Illuminate\\Database\\Connection->scalar('select exists (...')
#7 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(183): Illuminate\\Database\\Schema\\Builder->hasTable('migrations')
#8 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(749): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#9 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(164): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#10 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(338): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}(1)
#11 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(164): retry(1, Object(Closure), 0, Object(Closure))
#12 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(140): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#13 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#14 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#15 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#16 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#17 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#18 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#19 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#20 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#21 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#22 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#23 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#31 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it at C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:66)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(66): PDO->__construct('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#1 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(85): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#2 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection(Object(PDOException), 'mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#3 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#4 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1228): call_user_func(Object(Closure))
#7 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1264): Illuminate\\Database\\Connection->getPdo()
#8 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(509): Illuminate\\Database\\Connection->getReadPdo()
#9 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): Illuminate\\Database\\Connection->getPdoForSelect(true)
#10 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select exists (...', Array)
#11 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(976): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#12 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(955): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#13 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#14 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#15 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(341): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#16 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(358): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#17 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(174): Illuminate\\Database\\Connection->scalar('select exists (...')
#18 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(183): Illuminate\\Database\\Schema\\Builder->hasTable('migrations')
#19 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(749): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#20 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(164): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#21 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(338): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}(1)
#22 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(164): retry(1, Object(Closure), 0, Object(Closure))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(140): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#24 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#25 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#26 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#28 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#29 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#30 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#33 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#34 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#42 {main}
"} 
[2025-05-28 14:17:38] local.ERROR: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select count(*) as aggregate from `telescope_entries` where `type` = exception and `family_hash` = 9807572c25abfb46358a1a2af76a38b0) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select count(*) as aggregate from `telescope_entries` where `type` = exception and `family_hash` = 9807572c25abfb46358a1a2af76a38b0) at C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(976): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(955): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select count(*)...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select count(*)...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3107): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#5 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3092): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3679): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3605): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3533): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#10 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(123): Illuminate\\Database\\Query\\Builder->count()
#11 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(165): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->countExceptionOccurences(Object(Laravel\\Telescope\\IncomingExceptionEntry))
#12 [internal function]: Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Laravel\\Telescope\\IncomingExceptionEntry), 1)
#13 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(609): array_map(Object(Closure), Array, Array)
#14 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(800): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#15 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(164): Illuminate\\Support\\Collection->map(Object(Closure))
#16 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(274): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#17 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(163): Illuminate\\Support\\Collection->each(Object(Closure))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(140): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->storeExceptions(Object(Illuminate\\Support\\Collection))
#19 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#20 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#21 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Telescope.php(286): call_user_func(Object(Closure))
#22 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#25 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#29 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#31 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 1)
#32 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#33 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it at C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:66)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(66): PDO->__construct('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#1 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(85): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#2 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection(Object(PDOException), 'mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#3 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#4 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1228): call_user_func(Object(Closure))
#7 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1264): Illuminate\\Database\\Connection->getPdo()
#8 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(509): Illuminate\\Database\\Connection->getReadPdo()
#9 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): Illuminate\\Database\\Connection->getPdoForSelect(true)
#10 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select count(*)...', Array)
#11 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(976): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#12 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(955): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select count(*)...', Array, Object(Closure))
#13 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select count(*)...', Array, Object(Closure))
#14 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#15 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3107): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#16 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3092): Illuminate\\Database\\Query\\Builder->runSelect()
#17 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3679): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#18 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#19 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3605): Illuminate\\Database\\Query\\Builder->get(Array)
#20 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3533): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#21 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(123): Illuminate\\Database\\Query\\Builder->count()
#22 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(165): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->countExceptionOccurences(Object(Laravel\\Telescope\\IncomingExceptionEntry))
#23 [internal function]: Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Laravel\\Telescope\\IncomingExceptionEntry), 1)
#24 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(609): array_map(Object(Closure), Array, Array)
#25 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(800): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(164): Illuminate\\Support\\Collection->map(Object(Closure))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(274): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#28 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(163): Illuminate\\Support\\Collection->each(Object(Closure))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(140): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->storeExceptions(Object(Illuminate\\Support\\Collection))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Telescope.php(661): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#31 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#32 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Telescope.php(286): call_user_func(Object(Closure))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\Telescope.php(653): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\telescope\\src\\ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#36 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#37 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#40 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1451): Illuminate\\Container\\Container->call(Object(Closure))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(218): Illuminate\\Foundation\\Application->terminate()
#42 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1239): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 1)
#43 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#44 {main}
"} 
[2025-05-28 17:25:33] local.WARNING: Failed to create trace {"error":"cURL error 77: error setting certificate file: D:\\Projects\\Laragon-installer\\7.0-W64\\etc\\ssl\\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://cloud.langfuse.com/api/public/traces","trace":"#0 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlFactory.php(205): GuzzleHttp\\Handler\\CurlFactory::createRejection(Object(GuzzleHttp\\Handler\\EasyHandle), Array)
#1 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlFactory.php(157): GuzzleHttp\\Handler\\CurlFactory::finishError(Object(GuzzleHttp\\Handler\\CurlHandler), Object(GuzzleHttp\\Handler\\EasyHandle), Object(GuzzleHttp\\Handler\\CurlFactory))
#2 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlHandler.php(47): GuzzleHttp\\Handler\\CurlFactory::finish(Object(GuzzleHttp\\Handler\\CurlHandler), Object(GuzzleHttp\\Handler\\EasyHandle), Object(GuzzleHttp\\Handler\\CurlFactory))
#3 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\Proxy.php(28): GuzzleHttp\\Handler\\CurlHandler->__invoke(Object(GuzzleHttp\\Psr7\\Request), Array)
#4 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\Proxy.php(48): GuzzleHttp\\Handler\\Proxy::GuzzleHttp\\Handler\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#5 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\PrepareBodyMiddleware.php(64): GuzzleHttp\\Handler\\Proxy::GuzzleHttp\\Handler\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#6 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(31): GuzzleHttp\\PrepareBodyMiddleware->__invoke(Object(GuzzleHttp\\Psr7\\Request), Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\RedirectMiddleware.php(71): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#8 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(66): GuzzleHttp\\RedirectMiddleware->__invoke(Object(GuzzleHttp\\Psr7\\Request), Array)
#9 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\HandlerStack.php(75): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#10 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(333): GuzzleHttp\\HandlerStack->__invoke(Object(GuzzleHttp\\Psr7\\Request), Array)
#11 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(169): GuzzleHttp\\Client->transfer(Object(GuzzleHttp\\Psr7\\Request), Array)
#12 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(189): GuzzleHttp\\Client->requestAsync('POST', Object(GuzzleHttp\\Psr7\\Uri), Array)
#13 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Services\\Langfuse\\LangfuseService.php(204): GuzzleHttp\\Client->request('POST', 'traces', Array)
#14 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Services\\Langfuse\\LangfuseService.php(68): App\\Services\\Langfuse\\LangfuseService->makeRequest('POST', 'traces', Array)
#15 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Console\\Commands\\TestLangfuseIntegration.php(108): App\\Services\\Langfuse\\LangfuseService->createTrace(Array)
#16 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Console\\Commands\\TestLangfuseIntegration.php(49): App\\Console\\Commands\\TestLangfuseIntegration->testConnectivity()
#17 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Console\\Commands\\TestLangfuseIntegration->handle()
#18 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#19 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#20 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#21 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#22 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#23 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(App\\Console\\Commands\\TestLangfuseIntegration), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#31 {main}"} 
[2025-05-28 17:44:04] local.WARNING: Failed to create trace {"error":"cURL error 77: error setting certificate file: D:\\Projects\\Laragon-installer\\7.0-W64\\etc\\ssl\\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://cloud.langfuse.com/api/public/traces","trace":"#0 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlFactory.php(205): GuzzleHttp\\Handler\\CurlFactory::createRejection(Object(GuzzleHttp\\Handler\\EasyHandle), Array)
#1 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlFactory.php(157): GuzzleHttp\\Handler\\CurlFactory::finishError(Object(GuzzleHttp\\Handler\\CurlHandler), Object(GuzzleHttp\\Handler\\EasyHandle), Object(GuzzleHttp\\Handler\\CurlFactory))
#2 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlHandler.php(47): GuzzleHttp\\Handler\\CurlFactory::finish(Object(GuzzleHttp\\Handler\\CurlHandler), Object(GuzzleHttp\\Handler\\EasyHandle), Object(GuzzleHttp\\Handler\\CurlFactory))
#3 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\Proxy.php(28): GuzzleHttp\\Handler\\CurlHandler->__invoke(Object(GuzzleHttp\\Psr7\\Request), Array)
#4 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\Proxy.php(48): GuzzleHttp\\Handler\\Proxy::GuzzleHttp\\Handler\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#5 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\PrepareBodyMiddleware.php(64): GuzzleHttp\\Handler\\Proxy::GuzzleHttp\\Handler\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#6 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(31): GuzzleHttp\\PrepareBodyMiddleware->__invoke(Object(GuzzleHttp\\Psr7\\Request), Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\RedirectMiddleware.php(71): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#8 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(66): GuzzleHttp\\RedirectMiddleware->__invoke(Object(GuzzleHttp\\Psr7\\Request), Array)
#9 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\HandlerStack.php(75): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#10 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(333): GuzzleHttp\\HandlerStack->__invoke(Object(GuzzleHttp\\Psr7\\Request), Array)
#11 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(169): GuzzleHttp\\Client->transfer(Object(GuzzleHttp\\Psr7\\Request), Array)
#12 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(189): GuzzleHttp\\Client->requestAsync('POST', Object(GuzzleHttp\\Psr7\\Uri), Array)
#13 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Services\\Langfuse\\LangfuseService.php(212): GuzzleHttp\\Client->request('POST', 'traces', Array)
#14 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Services\\Langfuse\\LangfuseService.php(76): App\\Services\\Langfuse\\LangfuseService->makeRequest('POST', 'traces', Array)
#15 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Console\\Commands\\TestLangfuseIntegration.php(108): App\\Services\\Langfuse\\LangfuseService->createTrace(Array)
#16 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Console\\Commands\\TestLangfuseIntegration.php(49): App\\Console\\Commands\\TestLangfuseIntegration->testConnectivity()
#17 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Console\\Commands\\TestLangfuseIntegration->handle()
#18 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#19 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#20 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#21 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#22 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#23 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(App\\Console\\Commands\\TestLangfuseIntegration), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#31 {main}"} 
[2025-05-28 17:44:51] local.WARNING: Failed to create trace {"error":"cURL error 77: error setting certificate file: D:\\Projects\\Laragon-installer\\7.0-W64\\etc\\ssl\\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://cloud.langfuse.com/api/public/traces","trace":"#0 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlFactory.php(205): GuzzleHttp\\Handler\\CurlFactory::createRejection(Object(GuzzleHttp\\Handler\\EasyHandle), Array)
#1 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlFactory.php(157): GuzzleHttp\\Handler\\CurlFactory::finishError(Object(GuzzleHttp\\Handler\\CurlHandler), Object(GuzzleHttp\\Handler\\EasyHandle), Object(GuzzleHttp\\Handler\\CurlFactory))
#2 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlHandler.php(47): GuzzleHttp\\Handler\\CurlFactory::finish(Object(GuzzleHttp\\Handler\\CurlHandler), Object(GuzzleHttp\\Handler\\EasyHandle), Object(GuzzleHttp\\Handler\\CurlFactory))
#3 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\Proxy.php(28): GuzzleHttp\\Handler\\CurlHandler->__invoke(Object(GuzzleHttp\\Psr7\\Request), Array)
#4 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\Proxy.php(48): GuzzleHttp\\Handler\\Proxy::GuzzleHttp\\Handler\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#5 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\PrepareBodyMiddleware.php(64): GuzzleHttp\\Handler\\Proxy::GuzzleHttp\\Handler\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#6 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(31): GuzzleHttp\\PrepareBodyMiddleware->__invoke(Object(GuzzleHttp\\Psr7\\Request), Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\RedirectMiddleware.php(71): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#8 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(66): GuzzleHttp\\RedirectMiddleware->__invoke(Object(GuzzleHttp\\Psr7\\Request), Array)
#9 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\HandlerStack.php(75): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#10 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(333): GuzzleHttp\\HandlerStack->__invoke(Object(GuzzleHttp\\Psr7\\Request), Array)
#11 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(169): GuzzleHttp\\Client->transfer(Object(GuzzleHttp\\Psr7\\Request), Array)
#12 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(189): GuzzleHttp\\Client->requestAsync('POST', Object(GuzzleHttp\\Psr7\\Uri), Array)
#13 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Services\\Langfuse\\LangfuseService.php(212): GuzzleHttp\\Client->request('POST', 'traces', Array)
#14 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Services\\Langfuse\\LangfuseService.php(76): App\\Services\\Langfuse\\LangfuseService->makeRequest('POST', 'traces', Array)
#15 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Console\\Commands\\TestLangfuseIntegration.php(108): App\\Services\\Langfuse\\LangfuseService->createTrace(Array)
#16 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Console\\Commands\\TestLangfuseIntegration.php(49): App\\Console\\Commands\\TestLangfuseIntegration->testConnectivity()
#17 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Console\\Commands\\TestLangfuseIntegration->handle()
#18 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#19 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#20 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#21 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#22 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#23 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(App\\Console\\Commands\\TestLangfuseIntegration), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#31 {main}"} 
[2025-05-28 17:45:52] local.WARNING: Failed to create trace {"error":"cURL error 77: error setting certificate file: D:\\Projects\\Laragon-installer\\7.0-W64\\etc\\ssl\\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://cloud.langfuse.com/api/public/traces","trace":"#0 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlFactory.php(205): GuzzleHttp\\Handler\\CurlFactory::createRejection(Object(GuzzleHttp\\Handler\\EasyHandle), Array)
#1 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlFactory.php(157): GuzzleHttp\\Handler\\CurlFactory::finishError(Object(GuzzleHttp\\Handler\\CurlHandler), Object(GuzzleHttp\\Handler\\EasyHandle), Object(GuzzleHttp\\Handler\\CurlFactory))
#2 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlHandler.php(47): GuzzleHttp\\Handler\\CurlFactory::finish(Object(GuzzleHttp\\Handler\\CurlHandler), Object(GuzzleHttp\\Handler\\EasyHandle), Object(GuzzleHttp\\Handler\\CurlFactory))
#3 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\Proxy.php(28): GuzzleHttp\\Handler\\CurlHandler->__invoke(Object(GuzzleHttp\\Psr7\\Request), Array)
#4 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\Proxy.php(48): GuzzleHttp\\Handler\\Proxy::GuzzleHttp\\Handler\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#5 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\PrepareBodyMiddleware.php(64): GuzzleHttp\\Handler\\Proxy::GuzzleHttp\\Handler\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#6 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(31): GuzzleHttp\\PrepareBodyMiddleware->__invoke(Object(GuzzleHttp\\Psr7\\Request), Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\RedirectMiddleware.php(71): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#8 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(66): GuzzleHttp\\RedirectMiddleware->__invoke(Object(GuzzleHttp\\Psr7\\Request), Array)
#9 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\HandlerStack.php(75): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#10 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(333): GuzzleHttp\\HandlerStack->__invoke(Object(GuzzleHttp\\Psr7\\Request), Array)
#11 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(169): GuzzleHttp\\Client->transfer(Object(GuzzleHttp\\Psr7\\Request), Array)
#12 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(189): GuzzleHttp\\Client->requestAsync('POST', Object(GuzzleHttp\\Psr7\\Uri), Array)
#13 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Services\\Langfuse\\LangfuseService.php(211): GuzzleHttp\\Client->request('POST', 'traces', Array)
#14 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Services\\Langfuse\\LangfuseService.php(75): App\\Services\\Langfuse\\LangfuseService->makeRequest('POST', 'traces', Array)
#15 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Console\\Commands\\TestLangfuseIntegration.php(108): App\\Services\\Langfuse\\LangfuseService->createTrace(Array)
#16 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Console\\Commands\\TestLangfuseIntegration.php(49): App\\Console\\Commands\\TestLangfuseIntegration->testConnectivity()
#17 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Console\\Commands\\TestLangfuseIntegration->handle()
#18 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#19 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#20 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#21 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#22 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#23 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(App\\Console\\Commands\\TestLangfuseIntegration), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#31 {main}"} 
[2025-05-28 17:57:39] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'admin_id' cannot be null (Connection: mysql, SQL: insert into `point_adjustments` (`amount`, `type`, `reason`, `admin_id`, `user_id`, `updated_at`, `created_at`) values (100, add, Initial points for guest user, ?, 1, 2025-05-28 17:57:38, 2025-05-28 17:57:38)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'admin_id' cannot be null (Connection: mysql, SQL: insert into `point_adjustments` (`amount`, `type`, `reason`, `admin_id`, `user_id`, `updated_at`, `created_at`) values (100, add, Initial points for guest user, ?, 1, 2025-05-28 17:57:38, 2025-05-28 17:57:38)) at C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `po...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `po...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `po...', Array, 'id')
#3 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3769): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `po...', Array, 'id')
#4 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2208): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#5 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1408): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#6 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1373): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1212): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#8 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(370): Illuminate\\Database\\Eloquent\\Model->save()
#9 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->Illuminate\\Database\\Eloquent\\Relations\\{closure}(Object(App\\Models\\PointAdjustment))
#10 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(367): tap(Object(App\\Models\\PointAdjustment), Object(Closure))
#11 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Models\\User.php(217): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->create(Array)
#12 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasEvents.php(454): App\\Models\\User->App\\Models\\{closure}()
#13 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Models\\User.php(214): Illuminate\\Database\\Eloquent\\Model::withoutEvents(Object(Closure))
#14 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Models\\User.php(76): App\\Models\\User->addSomePoints(100, 'Initial points ...')
#15 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Models\\User::App\\Models\\{closure}(Object(App\\Models\\User))
#16 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(286): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('eloquent.create...', Array)
#17 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(266): Illuminate\\Events\\Dispatcher->invokeListeners('eloquent.create...', Array, false)
#18 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasEvents.php(224): Illuminate\\Events\\Dispatcher->dispatch('eloquent.create...', Array)
#19 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1394): Illuminate\\Database\\Eloquent\\Model->fireModelEvent('created', false)
#20 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1212): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#21 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1192): Illuminate\\Database\\Eloquent\\Model->save()
#22 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\User))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1191): tap(Object(App\\Models\\User), Object(Closure))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Http\\Controllers\\Auth\\AnonymousUserController.php(34): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#25 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Auth\\AnonymousUserController->__invoke(Object(App\\Http\\Requests\\Auth\\AnonymousLoginRequest))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Auth\\AnonymousUserController), '__invoke')
#27 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#28 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#29 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#61 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#64 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\D...')
#65 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'admin_id' cannot be null at C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php:53)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(53): PDOStatement->execute()
#1 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}('insert into `po...', Array)
#2 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `po...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `po...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `po...', Array, 'id')
#5 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3769): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `po...', Array, 'id')
#6 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2208): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#7 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1408): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#8 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1373): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#9 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1212): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#10 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(370): Illuminate\\Database\\Eloquent\\Model->save()
#11 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->Illuminate\\Database\\Eloquent\\Relations\\{closure}(Object(App\\Models\\PointAdjustment))
#12 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(367): tap(Object(App\\Models\\PointAdjustment), Object(Closure))
#13 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Models\\User.php(217): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->create(Array)
#14 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasEvents.php(454): App\\Models\\User->App\\Models\\{closure}()
#15 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Models\\User.php(214): Illuminate\\Database\\Eloquent\\Model::withoutEvents(Object(Closure))
#16 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Models\\User.php(76): App\\Models\\User->addSomePoints(100, 'Initial points ...')
#17 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(459): App\\Models\\User::App\\Models\\{closure}(Object(App\\Models\\User))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(286): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('eloquent.create...', Array)
#19 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(266): Illuminate\\Events\\Dispatcher->invokeListeners('eloquent.create...', Array, false)
#20 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasEvents.php(224): Illuminate\\Events\\Dispatcher->dispatch('eloquent.create...', Array)
#21 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1394): Illuminate\\Database\\Eloquent\\Model->fireModelEvent('created', false)
#22 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1212): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1192): Illuminate\\Database\\Eloquent\\Model->save()
#24 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\User))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1191): tap(Object(App\\Models\\User), Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Http\\Controllers\\Auth\\AnonymousUserController.php(34): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#27 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Auth\\AnonymousUserController->__invoke(Object(App\\Http\\Requests\\Auth\\AnonymousLoginRequest))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Auth\\AnonymousUserController), '__invoke')
#29 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#30 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#31 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#63 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#64 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#66 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\D...')
#67 {main}
"} 
[2025-05-28 17:59:02] local.ERROR: Command "seed" is not defined.

Did you mean one of these?
    db:seed
    make:seeder {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"seed\" is not defined.

Did you mean one of these?
    db:seed
    make:seeder at C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\symfony\\console\\Application.php:726)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('seed')
#1 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#5 {main}
"} 
[2025-05-28 17:59:26] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:26] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:26] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:26] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:26] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:26] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:26] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:26] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:27] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:27] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:28] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:28] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:28] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:28] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:28] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:28] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:28] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:28] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:29] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:29] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:29] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:29] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:29] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:29] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:29] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:29] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:29] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:30] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:30] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:30] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:30] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:30] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:30] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:30] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:30] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:30] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:31] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:31] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:31] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:31] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:31] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:31] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:31] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:31] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:32] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:32] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:32] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:32] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:32] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:32] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:32] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:32] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:33] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:33] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:33] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:33] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:33] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:33] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:33] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:33] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:34] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:34] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:34] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:34] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:34] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:34] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:34] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:34] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:34] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:35] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:35] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:35] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:35] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:35] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:35] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:35] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:35] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:35] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:35] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:36] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:36] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:36] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:36] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:36] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:36] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:36] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:36] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:36] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:37] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:37] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:37] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:37] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:37] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:37] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:37] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:37] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:37] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:37] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:38] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:38] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:38] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:38] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:38] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:38] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:38] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:38] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:38] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:39] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:39] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:39] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:39] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:39] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:39] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:39] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:39] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:39] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:40] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:40] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:40] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:40] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:40] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:40] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:40] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:40] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:40] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:40] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:41] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:41] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:41] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:41] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:41] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:41] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:41] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:41] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:41] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:41] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:42] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:42] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:42] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:42] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:42] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:42] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:42] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:42] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:42] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:43] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:43] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:43] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:43] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:43] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:43] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:43] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:43] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:44] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:44] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:44] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:44] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:44] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:44] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:45] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:45] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:45] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:45] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:45] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:45] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:45] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:45] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:46] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:46] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:46] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:46] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:46] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:46] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:46] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:46] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:46] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:47] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:47] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:47] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:47] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:47] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:47] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:47] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:47] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:47] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:48] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:48] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:48] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:48] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:48] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:48] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:48] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:48] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:48] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:48] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:49] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:49] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:49] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:49] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:49] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:49] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:49] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:49] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:49] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:49] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:50] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:50] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:50] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:50] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:50] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:50] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:50] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:50] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:50] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 17:59:50] local.INFO: Chat messages seeded successfully!  
[2025-05-28 18:01:54] local.ERROR: Route [login] not defined. {"exception":"[object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [login] not defined. at C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:526)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(853): Illuminate\\Routing\\UrlGenerator->route('login', Array, true)
#1 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Configuration\\ApplicationBuilder.php(278): route('login')
#2 [internal function]: Illuminate\\Foundation\\Configuration\\ApplicationBuilder->Illuminate\\Foundation\\Configuration\\{closure}(Object(Illuminate\\Http\\Request))
#3 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(117): call_user_func(Object(Closure), Object(Illuminate\\Http\\Request))
#4 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(104): Illuminate\\Auth\\Middleware\\Authenticate->redirectTo(Object(Illuminate\\Http\\Request))
#5 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(87): Illuminate\\Auth\\Middleware\\Authenticate->unauthenticated(Object(Illuminate\\Http\\Request), Array)
#6 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(61): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#8 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#10 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#12 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#13 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\D...')
#41 {main}
"} 
[2025-05-28 18:02:26] local.ERROR: Error during agent transition analysis {"error":"cURL error 77: error setting certificate file: D:\\Projects\\Laragon-installer\\7.0-W64\\etc\\ssl\\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions","chat_session_id":51} 
[2025-05-28 18:02:26] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 18:02:26] local.INFO: AI message created {"message_id":461} 
[2025-05-28 18:02:30] local.INFO: Starting OpenAI API usage tracking {"model":"gpt-4","has_chat_message":"yes","chat_message_id":461,"request_type":"chat_stream","endpoint":"/v1/chat/completions","metadata":{"prompt_length":1466,"response_length":34,"is_streamed":true,"request_type":"chat_stream","endpoint":"/v1/chat/completions","source":"SendMessageController","action":"send_message","chat_session_id":51,"user_message_id":460}} 
[2025-05-28 18:02:30] local.INFO: Extracted token usage {"prompt_tokens":367.0,"completion_tokens":9.0,"total_tokens":376.0} 
[2025-05-28 18:02:30] local.INFO: Attempting to track OpenAI API usage {"model":"gpt-4","has_chat_message":"yes","chat_message_id":461,"source":"SendMessageController","metadata":{"prompt_length":1466,"response_length":34,"is_streamed":true,"request_type":"chat_stream","endpoint":"/v1/chat/completions","source":"SendMessageController","action":"send_message","chat_session_id":51,"user_message_id":460}} 
[2025-05-28 18:02:30] local.INFO: Calculated costs {"prompt_cost":0.01101,"completion_cost":0.00054,"total_cost":0.011550000000000001} 
[2025-05-28 18:02:30] local.INFO: Creating OpenAI API usage record {"user_id":24,"chat_message_id":461,"model":"gpt-4","source":"SendMessageController","action":"send_message"} 
[2025-05-28 18:02:30] local.INFO: Successfully created OpenAI API usage record {"api_usage_id":1,"model":"gpt-4","total_tokens":376,"total_cost":"0.011550"} 
[2025-05-28 18:02:30] local.INFO: Successfully tracked OpenAI API usage {"model":"gpt-4","source":"SendMessageController"} 
[2025-05-28 18:02:30] local.WARNING: Failed to create trace {"error":"cURL error 77: error setting certificate file: D:\\Projects\\Laragon-installer\\7.0-W64\\etc\\ssl\\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://cloud.langfuse.com/api/public/traces","trace":"#0 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlFactory.php(205): GuzzleHttp\\Handler\\CurlFactory::createRejection(Object(GuzzleHttp\\Handler\\EasyHandle), Array)
#1 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlFactory.php(157): GuzzleHttp\\Handler\\CurlFactory::finishError(Object(GuzzleHttp\\Handler\\CurlHandler), Object(GuzzleHttp\\Handler\\EasyHandle), Object(GuzzleHttp\\Handler\\CurlFactory))
#2 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlHandler.php(47): GuzzleHttp\\Handler\\CurlFactory::finish(Object(GuzzleHttp\\Handler\\CurlHandler), Object(GuzzleHttp\\Handler\\EasyHandle), Object(GuzzleHttp\\Handler\\CurlFactory))
#3 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\Proxy.php(28): GuzzleHttp\\Handler\\CurlHandler->__invoke(Object(GuzzleHttp\\Psr7\\Request), Array)
#4 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\Proxy.php(48): GuzzleHttp\\Handler\\Proxy::GuzzleHttp\\Handler\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#5 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\PrepareBodyMiddleware.php(64): GuzzleHttp\\Handler\\Proxy::GuzzleHttp\\Handler\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#6 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(31): GuzzleHttp\\PrepareBodyMiddleware->__invoke(Object(GuzzleHttp\\Psr7\\Request), Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\RedirectMiddleware.php(71): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#8 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(66): GuzzleHttp\\RedirectMiddleware->__invoke(Object(GuzzleHttp\\Psr7\\Request), Array)
#9 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\HandlerStack.php(75): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#10 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(333): GuzzleHttp\\HandlerStack->__invoke(Object(GuzzleHttp\\Psr7\\Request), Array)
#11 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(169): GuzzleHttp\\Client->transfer(Object(GuzzleHttp\\Psr7\\Request), Array)
#12 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(189): GuzzleHttp\\Client->requestAsync('POST', Object(GuzzleHttp\\Psr7\\Uri), Array)
#13 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Services\\Langfuse\\LangfuseService.php(211): GuzzleHttp\\Client->request('POST', 'traces', Array)
#14 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Services\\Langfuse\\LangfuseService.php(75): App\\Services\\Langfuse\\LangfuseService->makeRequest('POST', 'traces', Array)
#15 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Services\\Langfuse\\LangfuseTrackingService.php(222): App\\Services\\Langfuse\\LangfuseService->createTrace(Array)
#16 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Services\\Langfuse\\LangfuseTrackingService.php(80): App\\Services\\Langfuse\\LangfuseTrackingService->createMessageTrace(Object(App\\Models\\ChatMessage), 'chat_session_51')
#17 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Services\\AI\\OpenAIService.php(208): App\\Services\\Langfuse\\LangfuseTrackingService->trackChatMessage(Object(App\\Models\\ChatMessage), 'You are a helpf...', Array)
#18 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Services\\AI\\OpenAIService.php(186): App\\Services\\AI\\OpenAIService->trackInLangfuse(461, 'You are a helpf...', Array)
#19 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Http\\Controllers\\Chat\\SendMessageController.php(437): App\\Services\\AI\\OpenAIService->streamResponse('You are a helpf...', Object(Closure), Array)
#20 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Http\\Controllers\\Chat\\SendMessageController.php(92): App\\Http\\Controllers\\Chat\\SendMessageController->handleRegularAiStream(Array)
#21 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\symfony\\http-foundation\\StreamedResponse.php(106): App\\Http\\Controllers\\Chat\\SendMessageController->App\\Http\\Controllers\\Chat\\{closure}()
#22 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\symfony\\http-foundation\\Response.php(395): Symfony\\Component\\HttpFoundation\\StreamedResponse->sendContent()
#23 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Symfony\\Component\\HttpFoundation\\Response->send()
#24 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\D...')
#26 {main}"} 
[2025-05-28 18:03:46] local.ERROR: Error during agent transition analysis {"error":"cURL error 77: error setting certificate file: D:\\Projects\\Laragon-installer\\7.0-W64\\etc\\ssl\\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions","chat_session_id":52} 
[2025-05-28 18:03:46] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 18:03:46] local.INFO: AI message created {"message_id":463} 
[2025-05-28 18:03:47] local.INFO: Starting OpenAI API usage tracking {"model":"gpt-4","has_chat_message":"yes","chat_message_id":463,"request_type":"chat_stream","endpoint":"/v1/chat/completions","metadata":{"prompt_length":1466,"response_length":34,"is_streamed":true,"request_type":"chat_stream","endpoint":"/v1/chat/completions","source":"SendMessageController","action":"send_message","chat_session_id":52,"user_message_id":462}} 
[2025-05-28 18:03:47] local.INFO: Extracted token usage {"prompt_tokens":367.0,"completion_tokens":9.0,"total_tokens":376.0} 
[2025-05-28 18:03:47] local.INFO: Attempting to track OpenAI API usage {"model":"gpt-4","has_chat_message":"yes","chat_message_id":463,"source":"SendMessageController","metadata":{"prompt_length":1466,"response_length":34,"is_streamed":true,"request_type":"chat_stream","endpoint":"/v1/chat/completions","source":"SendMessageController","action":"send_message","chat_session_id":52,"user_message_id":462}} 
[2025-05-28 18:03:47] local.INFO: Calculated costs {"prompt_cost":0.01101,"completion_cost":0.00054,"total_cost":0.011550000000000001} 
[2025-05-28 18:03:47] local.INFO: Creating OpenAI API usage record {"user_id":24,"chat_message_id":463,"model":"gpt-4","source":"SendMessageController","action":"send_message"} 
[2025-05-28 18:03:47] local.INFO: Successfully created OpenAI API usage record {"api_usage_id":2,"model":"gpt-4","total_tokens":376,"total_cost":"0.011550"} 
[2025-05-28 18:03:47] local.INFO: Successfully tracked OpenAI API usage {"model":"gpt-4","source":"SendMessageController"} 
[2025-05-28 18:03:47] local.WARNING: Failed to create trace {"error":"cURL error 77: error setting certificate file: D:\\Projects\\Laragon-installer\\7.0-W64\\etc\\ssl\\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://cloud.langfuse.com/api/public/traces","trace":"#0 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlFactory.php(205): GuzzleHttp\\Handler\\CurlFactory::createRejection(Object(GuzzleHttp\\Handler\\EasyHandle), Array)
#1 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlFactory.php(157): GuzzleHttp\\Handler\\CurlFactory::finishError(Object(GuzzleHttp\\Handler\\CurlHandler), Object(GuzzleHttp\\Handler\\EasyHandle), Object(GuzzleHttp\\Handler\\CurlFactory))
#2 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlHandler.php(47): GuzzleHttp\\Handler\\CurlFactory::finish(Object(GuzzleHttp\\Handler\\CurlHandler), Object(GuzzleHttp\\Handler\\EasyHandle), Object(GuzzleHttp\\Handler\\CurlFactory))
#3 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\Proxy.php(28): GuzzleHttp\\Handler\\CurlHandler->__invoke(Object(GuzzleHttp\\Psr7\\Request), Array)
#4 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\Proxy.php(48): GuzzleHttp\\Handler\\Proxy::GuzzleHttp\\Handler\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#5 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\PrepareBodyMiddleware.php(64): GuzzleHttp\\Handler\\Proxy::GuzzleHttp\\Handler\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#6 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(31): GuzzleHttp\\PrepareBodyMiddleware->__invoke(Object(GuzzleHttp\\Psr7\\Request), Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\RedirectMiddleware.php(71): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#8 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(66): GuzzleHttp\\RedirectMiddleware->__invoke(Object(GuzzleHttp\\Psr7\\Request), Array)
#9 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\HandlerStack.php(75): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#10 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(333): GuzzleHttp\\HandlerStack->__invoke(Object(GuzzleHttp\\Psr7\\Request), Array)
#11 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(169): GuzzleHttp\\Client->transfer(Object(GuzzleHttp\\Psr7\\Request), Array)
#12 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(189): GuzzleHttp\\Client->requestAsync('POST', Object(GuzzleHttp\\Psr7\\Uri), Array)
#13 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Services\\Langfuse\\LangfuseService.php(211): GuzzleHttp\\Client->request('POST', 'traces', Array)
#14 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Services\\Langfuse\\LangfuseService.php(75): App\\Services\\Langfuse\\LangfuseService->makeRequest('POST', 'traces', Array)
#15 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Services\\Langfuse\\LangfuseTrackingService.php(222): App\\Services\\Langfuse\\LangfuseService->createTrace(Array)
#16 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Services\\Langfuse\\LangfuseTrackingService.php(80): App\\Services\\Langfuse\\LangfuseTrackingService->createMessageTrace(Object(App\\Models\\ChatMessage), 'chat_session_52')
#17 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Services\\AI\\OpenAIService.php(208): App\\Services\\Langfuse\\LangfuseTrackingService->trackChatMessage(Object(App\\Models\\ChatMessage), 'You are a helpf...', Array)
#18 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Services\\AI\\OpenAIService.php(186): App\\Services\\AI\\OpenAIService->trackInLangfuse(463, 'You are a helpf...', Array)
#19 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Http\\Controllers\\Chat\\SendMessageController.php(437): App\\Services\\AI\\OpenAIService->streamResponse('You are a helpf...', Object(Closure), Array)
#20 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Http\\Controllers\\Chat\\SendMessageController.php(92): App\\Http\\Controllers\\Chat\\SendMessageController->handleRegularAiStream(Array)
#21 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\symfony\\http-foundation\\StreamedResponse.php(106): App\\Http\\Controllers\\Chat\\SendMessageController->App\\Http\\Controllers\\Chat\\{closure}()
#22 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\symfony\\http-foundation\\Response.php(395): Symfony\\Component\\HttpFoundation\\StreamedResponse->sendContent()
#23 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Symfony\\Component\\HttpFoundation\\Response->send()
#24 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\D...')
#26 {main}"} 
[2025-05-28 18:04:13] local.ERROR: Error during agent transition analysis {"error":"cURL error 77: error setting certificate file: D:\\Projects\\Laragon-installer\\7.0-W64\\etc\\ssl\\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions","chat_session_id":53} 
[2025-05-28 18:04:13] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 18:04:13] local.INFO: AI message created {"message_id":465} 
[2025-05-28 18:04:15] local.INFO: Starting OpenAI API usage tracking {"model":"gpt-4","has_chat_message":"yes","chat_message_id":465,"request_type":"chat_stream","endpoint":"/v1/chat/completions","metadata":{"prompt_length":1466,"response_length":34,"is_streamed":true,"request_type":"chat_stream","endpoint":"/v1/chat/completions","source":"SendMessageController","action":"send_message","chat_session_id":53,"user_message_id":464}} 
[2025-05-28 18:04:15] local.INFO: Extracted token usage {"prompt_tokens":367.0,"completion_tokens":9.0,"total_tokens":376.0} 
[2025-05-28 18:04:15] local.INFO: Attempting to track OpenAI API usage {"model":"gpt-4","has_chat_message":"yes","chat_message_id":465,"source":"SendMessageController","metadata":{"prompt_length":1466,"response_length":34,"is_streamed":true,"request_type":"chat_stream","endpoint":"/v1/chat/completions","source":"SendMessageController","action":"send_message","chat_session_id":53,"user_message_id":464}} 
[2025-05-28 18:04:15] local.INFO: Calculated costs {"prompt_cost":0.01101,"completion_cost":0.00054,"total_cost":0.011550000000000001} 
[2025-05-28 18:04:15] local.INFO: Creating OpenAI API usage record {"user_id":24,"chat_message_id":465,"model":"gpt-4","source":"SendMessageController","action":"send_message"} 
[2025-05-28 18:04:15] local.INFO: Successfully created OpenAI API usage record {"api_usage_id":3,"model":"gpt-4","total_tokens":376,"total_cost":"0.011550"} 
[2025-05-28 18:04:15] local.INFO: Successfully tracked OpenAI API usage {"model":"gpt-4","source":"SendMessageController"} 
[2025-05-28 18:04:15] local.WARNING: Failed to create trace {"error":"cURL error 77: error setting certificate file: D:\\Projects\\Laragon-installer\\7.0-W64\\etc\\ssl\\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://cloud.langfuse.com/api/public/traces","trace":"#0 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlFactory.php(205): GuzzleHttp\\Handler\\CurlFactory::createRejection(Object(GuzzleHttp\\Handler\\EasyHandle), Array)
#1 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlFactory.php(157): GuzzleHttp\\Handler\\CurlFactory::finishError(Object(GuzzleHttp\\Handler\\CurlHandler), Object(GuzzleHttp\\Handler\\EasyHandle), Object(GuzzleHttp\\Handler\\CurlFactory))
#2 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlHandler.php(47): GuzzleHttp\\Handler\\CurlFactory::finish(Object(GuzzleHttp\\Handler\\CurlHandler), Object(GuzzleHttp\\Handler\\EasyHandle), Object(GuzzleHttp\\Handler\\CurlFactory))
#3 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\Proxy.php(28): GuzzleHttp\\Handler\\CurlHandler->__invoke(Object(GuzzleHttp\\Psr7\\Request), Array)
#4 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\Proxy.php(48): GuzzleHttp\\Handler\\Proxy::GuzzleHttp\\Handler\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#5 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\PrepareBodyMiddleware.php(64): GuzzleHttp\\Handler\\Proxy::GuzzleHttp\\Handler\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#6 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(31): GuzzleHttp\\PrepareBodyMiddleware->__invoke(Object(GuzzleHttp\\Psr7\\Request), Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\RedirectMiddleware.php(71): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#8 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(66): GuzzleHttp\\RedirectMiddleware->__invoke(Object(GuzzleHttp\\Psr7\\Request), Array)
#9 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\HandlerStack.php(75): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#10 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(333): GuzzleHttp\\HandlerStack->__invoke(Object(GuzzleHttp\\Psr7\\Request), Array)
#11 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(169): GuzzleHttp\\Client->transfer(Object(GuzzleHttp\\Psr7\\Request), Array)
#12 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(189): GuzzleHttp\\Client->requestAsync('POST', Object(GuzzleHttp\\Psr7\\Uri), Array)
#13 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Services\\Langfuse\\LangfuseService.php(211): GuzzleHttp\\Client->request('POST', 'traces', Array)
#14 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Services\\Langfuse\\LangfuseService.php(75): App\\Services\\Langfuse\\LangfuseService->makeRequest('POST', 'traces', Array)
#15 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Services\\Langfuse\\LangfuseTrackingService.php(222): App\\Services\\Langfuse\\LangfuseService->createTrace(Array)
#16 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Services\\Langfuse\\LangfuseTrackingService.php(80): App\\Services\\Langfuse\\LangfuseTrackingService->createMessageTrace(Object(App\\Models\\ChatMessage), 'chat_session_53')
#17 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Services\\AI\\OpenAIService.php(208): App\\Services\\Langfuse\\LangfuseTrackingService->trackChatMessage(Object(App\\Models\\ChatMessage), 'You are a helpf...', Array)
#18 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Services\\AI\\OpenAIService.php(186): App\\Services\\AI\\OpenAIService->trackInLangfuse(465, 'You are a helpf...', Array)
#19 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Http\\Controllers\\Chat\\SendMessageController.php(437): App\\Services\\AI\\OpenAIService->streamResponse('You are a helpf...', Object(Closure), Array)
#20 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Http\\Controllers\\Chat\\SendMessageController.php(92): App\\Http\\Controllers\\Chat\\SendMessageController->handleRegularAiStream(Array)
#21 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\symfony\\http-foundation\\StreamedResponse.php(106): App\\Http\\Controllers\\Chat\\SendMessageController->App\\Http\\Controllers\\Chat\\{closure}()
#22 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\symfony\\http-foundation\\Response.php(395): Symfony\\Component\\HttpFoundation\\StreamedResponse->sendContent()
#23 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Symfony\\Component\\HttpFoundation\\Response->send()
#24 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\D...')
#26 {main}"} 
[2025-05-28 18:07:32] local.ERROR: PHP Parse error: Syntax error, unexpected '=' on line 1 {"exception":"[object] (Psy\\Exception\\ParseErrorException(code: 0): PHP Parse error: Syntax error, unexpected '=' on line 1 at C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\psy\\psysh\\src\\Exception\\ParseErrorException.php:44)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\psy\\psysh\\src\\CodeCleaner.php(306): Psy\\Exception\\ParseErrorException::fromParseError(Object(PhpParser\\Error))
#1 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\psy\\psysh\\src\\CodeCleaner.php(240): Psy\\CodeCleaner->parse('<?php  = \\\\App\\\\M...', false)
#2 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\psy\\psysh\\src\\Shell.php(852): Psy\\CodeCleaner->clean(Array, false)
#3 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\psy\\psysh\\src\\Shell.php(881): Psy\\Shell->addCode(' = \\\\App\\\\Models\\\\...', true)
#4 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\psy\\psysh\\src\\Shell.php(1390): Psy\\Shell->setCode(' = \\\\App\\\\Models\\\\...', true)
#5 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\tinker\\src\\Console\\TinkerCommand.php(76): Psy\\Shell->execute(' = \\\\App\\\\Models\\\\...')
#6 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Tinker\\Console\\TinkerCommand->handle()
#7 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#8 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#9 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#10 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#11 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#12 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#13 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Laravel\\Tinker\\Console\\TinkerCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#20 {main}
"} 
[2025-05-28 18:09:58] local.ERROR: Error during agent transition analysis {"error":"cURL error 77: error setting certificate file: D:\\Projects\\Laragon-installer\\7.0-W64\\etc\\ssl\\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions","chat_session_id":54} 
[2025-05-28 18:09:58] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 18:09:58] local.INFO: AI message created {"message_id":467} 
[2025-05-28 18:09:59] local.INFO: Starting OpenAI API usage tracking {"model":"gpt-4","has_chat_message":"yes","chat_message_id":467,"request_type":"chat_stream","endpoint":"/v1/chat/completions","metadata":{"prompt_length":1466,"response_length":34,"is_streamed":true,"request_type":"chat_stream","endpoint":"/v1/chat/completions","source":"SendMessageController","action":"send_message","chat_session_id":54,"user_message_id":466}} 
[2025-05-28 18:09:59] local.INFO: Extracted token usage {"prompt_tokens":367.0,"completion_tokens":9.0,"total_tokens":376.0} 
[2025-05-28 18:09:59] local.INFO: Attempting to track OpenAI API usage {"model":"gpt-4","has_chat_message":"yes","chat_message_id":467,"source":"SendMessageController","metadata":{"prompt_length":1466,"response_length":34,"is_streamed":true,"request_type":"chat_stream","endpoint":"/v1/chat/completions","source":"SendMessageController","action":"send_message","chat_session_id":54,"user_message_id":466}} 
[2025-05-28 18:09:59] local.INFO: Calculated costs {"prompt_cost":0.01101,"completion_cost":0.00054,"total_cost":0.011550000000000001} 
[2025-05-28 18:09:59] local.INFO: Creating OpenAI API usage record {"user_id":24,"chat_message_id":467,"model":"gpt-4","source":"SendMessageController","action":"send_message"} 
[2025-05-28 18:09:59] local.INFO: Successfully created OpenAI API usage record {"api_usage_id":4,"model":"gpt-4","total_tokens":376,"total_cost":"0.011550"} 
[2025-05-28 18:09:59] local.INFO: Successfully tracked OpenAI API usage {"model":"gpt-4","source":"SendMessageController"} 
[2025-05-28 18:12:30] local.ERROR: Error during agent transition analysis {"error":"cURL error 77: error setting certificate file: D:\\Projects\\Laragon-installer\\7.0-W64\\etc\\ssl\\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions","chat_session_id":54} 
[2025-05-28 18:12:30] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 18:12:30] local.INFO: AI message created {"message_id":469} 
[2025-05-28 18:12:34] local.INFO: Starting OpenAI API usage tracking {"model":"gpt-4","has_chat_message":"yes","chat_message_id":469,"request_type":"chat_stream","endpoint":"/v1/chat/completions","metadata":{"prompt_length":1543,"response_length":276,"is_streamed":true,"request_type":"chat_stream","endpoint":"/v1/chat/completions","source":"SendMessageController","action":"send_message","chat_session_id":54,"user_message_id":468}} 
[2025-05-28 18:12:34] local.INFO: Extracted token usage {"prompt_tokens":386.0,"completion_tokens":69.0,"total_tokens":455.0} 
[2025-05-28 18:12:34] local.INFO: Attempting to track OpenAI API usage {"model":"gpt-4","has_chat_message":"yes","chat_message_id":469,"source":"SendMessageController","metadata":{"prompt_length":1543,"response_length":276,"is_streamed":true,"request_type":"chat_stream","endpoint":"/v1/chat/completions","source":"SendMessageController","action":"send_message","chat_session_id":54,"user_message_id":468}} 
[2025-05-28 18:12:34] local.INFO: Calculated costs {"prompt_cost":0.01158,"completion_cost":0.0041400000000000005,"total_cost":0.01572} 
[2025-05-28 18:12:34] local.INFO: Creating OpenAI API usage record {"user_id":24,"chat_message_id":469,"model":"gpt-4","source":"SendMessageController","action":"send_message"} 
[2025-05-28 18:12:34] local.INFO: Successfully created OpenAI API usage record {"api_usage_id":5,"model":"gpt-4","total_tokens":455,"total_cost":"0.015720"} 
[2025-05-28 18:12:34] local.INFO: Successfully tracked OpenAI API usage {"model":"gpt-4","source":"SendMessageController"} 
[2025-05-28 18:23:32] local.ERROR: Error during agent transition analysis {"error":"cURL error 77: error setting certificate file: D:\\Projects\\Laragon-installer\\7.0-W64\\etc\\ssl\\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions","chat_session_id":54} 
[2025-05-28 18:23:32] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 18:23:32] local.INFO: AI message created {"message_id":471} 
[2025-05-28 18:23:33] local.ERROR: AI Response Generation Error {"error":"Connection refused for URI https://api.openai.com/v1/chat/completions","stack_trace":"#0 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\openai-php\\client\\src\\Transporters\\HttpTransporter.php(92): OpenAI\\Transporters\\HttpTransporter->sendRequest(Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\openai-php\\client\\src\\Resources\\Chat.php(52): OpenAI\\Transporters\\HttpTransporter->requestStream(Object(OpenAI\\ValueObjects\\Transporter\\Payload))
#2 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Services\\AI\\OpenAIService.php(123): OpenAI\\Resources\\Chat->createStreamed(Array)
#3 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Http\\Controllers\\Chat\\SendMessageController.php(437): App\\Services\\AI\\OpenAIService->streamResponse('You are a helpf...', Object(Closure), Array)
#4 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\app\\Http\\Controllers\\Chat\\SendMessageController.php(92): App\\Http\\Controllers\\Chat\\SendMessageController->handleRegularAiStream(Array)
#5 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\symfony\\http-foundation\\StreamedResponse.php(106): App\\Http\\Controllers\\Chat\\SendMessageController->App\\Http\\Controllers\\Chat\\{closure}()
#6 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\symfony\\http-foundation\\Response.php(395): Symfony\\Component\\HttpFoundation\\StreamedResponse->sendContent()
#7 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Symfony\\Component\\HttpFoundation\\Response->send()
#8 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#9 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\D...')
#10 {main}","chat_session_id":54,"user_message_id":470} 
[2025-05-28 18:24:11] local.ERROR: Error during agent transition analysis {"error":"cURL error 77: error setting certificate file: D:\\Projects\\Laragon-installer\\7.0-W64\\etc\\ssl\\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions","chat_session_id":54} 
[2025-05-28 18:24:11] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 18:24:11] local.INFO: AI message created {"message_id":473} 
[2025-05-28 18:24:14] local.INFO: Starting OpenAI API usage tracking {"model":"gpt-4","has_chat_message":"yes","chat_message_id":473,"request_type":"chat_stream","endpoint":"/v1/chat/completions","metadata":{"prompt_length":1913,"response_length":220,"is_streamed":true,"request_type":"chat_stream","endpoint":"/v1/chat/completions","source":"SendMessageController","action":"send_message","chat_session_id":54,"user_message_id":472}} 
[2025-05-28 18:24:14] local.INFO: Extracted token usage {"prompt_tokens":479.0,"completion_tokens":55.0,"total_tokens":534.0} 
[2025-05-28 18:24:14] local.INFO: Attempting to track OpenAI API usage {"model":"gpt-4","has_chat_message":"yes","chat_message_id":473,"source":"SendMessageController","metadata":{"prompt_length":1913,"response_length":220,"is_streamed":true,"request_type":"chat_stream","endpoint":"/v1/chat/completions","source":"SendMessageController","action":"send_message","chat_session_id":54,"user_message_id":472}} 
[2025-05-28 18:24:14] local.INFO: Calculated costs {"prompt_cost":0.01437,"completion_cost":0.0033,"total_cost":0.01767} 
[2025-05-28 18:24:14] local.INFO: Creating OpenAI API usage record {"user_id":24,"chat_message_id":473,"model":"gpt-4","source":"SendMessageController","action":"send_message"} 
[2025-05-28 18:24:14] local.INFO: Successfully created OpenAI API usage record {"api_usage_id":6,"model":"gpt-4","total_tokens":534,"total_cost":"0.017670"} 
[2025-05-28 18:24:14] local.INFO: Successfully tracked OpenAI API usage {"model":"gpt-4","source":"SendMessageController"} 
[2025-05-28 18:25:11] local.ERROR: Error during agent transition analysis {"error":"cURL error 77: error setting certificate file: D:\\Projects\\Laragon-installer\\7.0-W64\\etc\\ssl\\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions","chat_session_id":55} 
[2025-05-28 18:25:11] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 18:25:11] local.INFO: AI message created {"message_id":475} 
[2025-05-28 18:25:13] local.INFO: Starting OpenAI API usage tracking {"model":"gpt-4","has_chat_message":"yes","chat_message_id":475,"request_type":"chat_stream","endpoint":"/v1/chat/completions","metadata":{"prompt_length":1466,"response_length":34,"is_streamed":true,"request_type":"chat_stream","endpoint":"/v1/chat/completions","source":"SendMessageController","action":"send_message","chat_session_id":55,"user_message_id":474}} 
[2025-05-28 18:25:13] local.INFO: Extracted token usage {"prompt_tokens":367.0,"completion_tokens":9.0,"total_tokens":376.0} 
[2025-05-28 18:25:13] local.INFO: Attempting to track OpenAI API usage {"model":"gpt-4","has_chat_message":"yes","chat_message_id":475,"source":"SendMessageController","metadata":{"prompt_length":1466,"response_length":34,"is_streamed":true,"request_type":"chat_stream","endpoint":"/v1/chat/completions","source":"SendMessageController","action":"send_message","chat_session_id":55,"user_message_id":474}} 
[2025-05-28 18:25:13] local.INFO: Calculated costs {"prompt_cost":0.01101,"completion_cost":0.00054,"total_cost":0.011550000000000001} 
[2025-05-28 18:25:13] local.INFO: Creating OpenAI API usage record {"user_id":24,"chat_message_id":475,"model":"gpt-4","source":"SendMessageController","action":"send_message"} 
[2025-05-28 18:25:13] local.INFO: Successfully created OpenAI API usage record {"api_usage_id":7,"model":"gpt-4","total_tokens":376,"total_cost":"0.011550"} 
[2025-05-28 18:25:13] local.INFO: Successfully tracked OpenAI API usage {"model":"gpt-4","source":"SendMessageController"} 
[2025-05-28 18:25:35] local.ERROR: Error during agent transition analysis {"error":"cURL error 77: error setting certificate file: D:\\Projects\\Laragon-installer\\7.0-W64\\etc\\ssl\\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions","chat_session_id":55} 
[2025-05-28 18:25:35] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 18:25:35] local.INFO: AI message created {"message_id":477} 
[2025-05-28 18:25:38] local.INFO: Starting OpenAI API usage tracking {"model":"gpt-4","has_chat_message":"yes","chat_message_id":477,"request_type":"chat_stream","endpoint":"/v1/chat/completions","metadata":{"prompt_length":1543,"response_length":187,"is_streamed":true,"request_type":"chat_stream","endpoint":"/v1/chat/completions","source":"SendMessageController","action":"send_message","chat_session_id":55,"user_message_id":476}} 
[2025-05-28 18:25:38] local.INFO: Extracted token usage {"prompt_tokens":386.0,"completion_tokens":47.0,"total_tokens":433.0} 
[2025-05-28 18:25:38] local.INFO: Attempting to track OpenAI API usage {"model":"gpt-4","has_chat_message":"yes","chat_message_id":477,"source":"SendMessageController","metadata":{"prompt_length":1543,"response_length":187,"is_streamed":true,"request_type":"chat_stream","endpoint":"/v1/chat/completions","source":"SendMessageController","action":"send_message","chat_session_id":55,"user_message_id":476}} 
[2025-05-28 18:25:38] local.INFO: Calculated costs {"prompt_cost":0.01158,"completion_cost":0.00282,"total_cost":0.0144} 
[2025-05-28 18:25:38] local.INFO: Creating OpenAI API usage record {"user_id":24,"chat_message_id":477,"model":"gpt-4","source":"SendMessageController","action":"send_message"} 
[2025-05-28 18:25:38] local.INFO: Successfully created OpenAI API usage record {"api_usage_id":8,"model":"gpt-4","total_tokens":433,"total_cost":"0.014400"} 
[2025-05-28 18:25:38] local.INFO: Successfully tracked OpenAI API usage {"model":"gpt-4","source":"SendMessageController"} 
[2025-05-28 18:34:26] local.ERROR: Error during agent transition analysis {"error":"cURL error 77: error setting certificate file: D:\\Projects\\Laragon-installer\\7.0-W64\\etc\\ssl\\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions","chat_session_id":55} 
[2025-05-28 18:34:26] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 18:34:26] local.INFO: AI message created {"message_id":479} 
[2025-05-28 18:34:29] local.INFO: Starting OpenAI API usage tracking {"model":"gpt-4","has_chat_message":"yes","chat_message_id":479,"request_type":"chat_stream","endpoint":"/v1/chat/completions","metadata":{"prompt_length":1772,"response_length":201,"is_streamed":true,"request_type":"chat_stream","endpoint":"/v1/chat/completions","source":"SendMessageController","action":"send_message","chat_session_id":55,"user_message_id":478}} 
[2025-05-28 18:34:29] local.INFO: Extracted token usage {"prompt_tokens":443.0,"completion_tokens":51.0,"total_tokens":494.0} 
[2025-05-28 18:34:29] local.INFO: Attempting to track OpenAI API usage {"model":"gpt-4","has_chat_message":"yes","chat_message_id":479,"source":"SendMessageController","metadata":{"prompt_length":1772,"response_length":201,"is_streamed":true,"request_type":"chat_stream","endpoint":"/v1/chat/completions","source":"SendMessageController","action":"send_message","chat_session_id":55,"user_message_id":478}} 
[2025-05-28 18:34:29] local.INFO: Calculated costs {"prompt_cost":0.01329,"completion_cost":0.0030600000000000002,"total_cost":0.01635} 
[2025-05-28 18:34:29] local.INFO: Creating OpenAI API usage record {"user_id":24,"chat_message_id":479,"model":"gpt-4","source":"SendMessageController","action":"send_message"} 
[2025-05-28 18:34:29] local.INFO: Successfully created OpenAI API usage record {"api_usage_id":9,"model":"gpt-4","total_tokens":494,"total_cost":"0.016350"} 
[2025-05-28 18:34:29] local.INFO: Successfully tracked OpenAI API usage {"model":"gpt-4","source":"SendMessageController"} 
[2025-05-28 18:45:00] local.INFO: Creating Langfuse trace {"trace_id":"895ab05f-e28e-4e90-bfbb-45a6e74a239a","has_input":false,"has_output":false,"input_preview":null,"output_preview":null} 
[2025-05-28 18:46:07] local.INFO: Creating Langfuse trace {"trace_id":"1d16c5eb-1993-49b6-86f3-af81150b5aa8","has_input":true,"has_output":true,"input_preview":"Test trace input - user question","output_preview":"Test trace output - agent response"} 
[2025-05-28 18:46:51] local.ERROR: Error during agent transition analysis {"error":"cURL error 77: error setting certificate file: D:\\Projects\\Laragon-installer\\7.0-W64\\etc\\ssl\\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions","chat_session_id":55} 
[2025-05-28 18:46:51] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 18:46:51] local.INFO: AI message created {"message_id":481} 
[2025-05-28 18:46:54] local.INFO: Starting OpenAI API usage tracking {"model":"gpt-4","has_chat_message":"yes","chat_message_id":481,"request_type":"chat_stream","endpoint":"/v1/chat/completions","metadata":{"prompt_length":2010,"response_length":201,"is_streamed":true,"request_type":"chat_stream","endpoint":"/v1/chat/completions","source":"SendMessageController","action":"send_message","chat_session_id":55,"user_message_id":480}} 
[2025-05-28 18:46:54] local.INFO: Extracted token usage {"prompt_tokens":503.0,"completion_tokens":51.0,"total_tokens":554.0} 
[2025-05-28 18:46:54] local.INFO: Attempting to track OpenAI API usage {"model":"gpt-4","has_chat_message":"yes","chat_message_id":481,"source":"SendMessageController","metadata":{"prompt_length":2010,"response_length":201,"is_streamed":true,"request_type":"chat_stream","endpoint":"/v1/chat/completions","source":"SendMessageController","action":"send_message","chat_session_id":55,"user_message_id":480}} 
[2025-05-28 18:46:54] local.INFO: Calculated costs {"prompt_cost":0.015090000000000001,"completion_cost":0.0030600000000000002,"total_cost":0.01815} 
[2025-05-28 18:46:54] local.INFO: Creating OpenAI API usage record {"user_id":24,"chat_message_id":481,"model":"gpt-4","source":"SendMessageController","action":"send_message"} 
[2025-05-28 18:46:54] local.INFO: Successfully created OpenAI API usage record {"api_usage_id":10,"model":"gpt-4","total_tokens":554,"total_cost":"0.018150"} 
[2025-05-28 18:46:54] local.INFO: Successfully tracked OpenAI API usage {"model":"gpt-4","source":"SendMessageController"} 
[2025-05-28 18:46:54] local.INFO: Creating Langfuse trace {"trace_id":"172d1b61-69f4-4778-bdd3-566339c5ecaa","has_input":true,"has_output":true,"input_preview":"i need some places","output_preview":"Could you please specify what type of places you are looking for? Are you interested in entertainmen"} 
[2025-05-28 18:46:57] local.INFO: Creating Langfuse trace {"trace_id":"31041139-26ef-4453-afda-0cb68efe76e5","has_input":true,"has_output":true,"input_preview":"Test trace input - user question","output_preview":"Test trace output - agent response"} 
[2025-05-28 18:52:17] local.ERROR: Error during agent transition analysis {"error":"cURL error 77: error setting certificate file: D:\\Projects\\Laragon-installer\\7.0-W64\\etc\\ssl\\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions","chat_session_id":56} 
[2025-05-28 18:52:17] local.ERROR: Failed to generate chat title: cURL error 77: error setting certificate file: D:\Projects\Laragon-installer\7.0-W64\etc\ssl\cacert.pem (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.openai.com/v1/chat/completions  
[2025-05-28 18:52:17] local.INFO: AI message created {"message_id":483} 
[2025-05-28 18:52:18] local.INFO: Starting OpenAI API usage tracking {"model":"gpt-4","has_chat_message":"yes","chat_message_id":483,"request_type":"chat_stream","endpoint":"/v1/chat/completions","metadata":{"prompt_length":1466,"response_length":34,"is_streamed":true,"request_type":"chat_stream","endpoint":"/v1/chat/completions","source":"SendMessageController","action":"send_message","chat_session_id":56,"user_message_id":482}} 
[2025-05-28 18:52:18] local.INFO: Extracted token usage {"prompt_tokens":367.0,"completion_tokens":9.0,"total_tokens":376.0} 
[2025-05-28 18:52:18] local.INFO: Attempting to track OpenAI API usage {"model":"gpt-4","has_chat_message":"yes","chat_message_id":483,"source":"SendMessageController","metadata":{"prompt_length":1466,"response_length":34,"is_streamed":true,"request_type":"chat_stream","endpoint":"/v1/chat/completions","source":"SendMessageController","action":"send_message","chat_session_id":56,"user_message_id":482}} 
[2025-05-28 18:52:18] local.INFO: Calculated costs {"prompt_cost":0.01101,"completion_cost":0.00054,"total_cost":0.011550000000000001} 
[2025-05-28 18:52:18] local.INFO: Creating OpenAI API usage record {"user_id":24,"chat_message_id":483,"model":"gpt-4","source":"SendMessageController","action":"send_message"} 
[2025-05-28 18:52:18] local.INFO: Successfully created OpenAI API usage record {"api_usage_id":11,"model":"gpt-4","total_tokens":376,"total_cost":"0.011550"} 
[2025-05-28 18:52:18] local.INFO: Successfully tracked OpenAI API usage {"model":"gpt-4","source":"SendMessageController"} 
[2025-05-29 10:07:32] local.ERROR: __VSCODE_LARAVEL_STARTUP_ERROR__: rename(C:\Users\<USER>\Documents\GitHub\One Studio\rydo-backend\bootstrap\cache\ser501F.tmp,C:\Users\<USER>\Documents\GitHub\One Studio\rydo-backend\bootstrap\cache\services.php): Access is denied (code: 5) {"exception":"[object] (Error(code: 0): __VSCODE_LARAVEL_STARTUP_ERROR__: rename(C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\bootstrap\\cache\\ser501F.tmp,C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\bootstrap\\cache\\services.php): Access is denied (code: 5) at C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\_laravel_ide\\discover-ea280fb1636eb761c4fd23f76666e397.php:33)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\One Studio\\rydo-backend\\vendor\\_laravel_ide\\discover-ea280fb1636eb761c4fd23f76666e397.php(62): LaravelVsCode::startupError(Object(ErrorException))
#1 {main}
"} 

import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:oktoast/oktoast.dart';

import '../resources/app_colors.dart';
import '../resources/app_icons.dart';

enum ToastType { success, error, warning, info }

class NotificationAlert {
  static void showLocalNotification(
    String message, {
    ToastType type = ToastType.success,
    int seconds = 2,
    AlignmentGeometry align = Alignment.bottomCenter,
  }) {
    showToastWidget(
      NotificationItemView(message: message, toastType: type),
      duration: Duration(seconds: seconds),
      position: ToastPosition(
        align: align,
        offset: appContext.mediaPadding.top + 8.0,
      ),
    );
  }
}

class NotificationItemView extends StatelessWidget {
  const NotificationItemView({
    super.key,
    required this.message,
    required this.toastType,
  });

  final String message;
  final ToastType toastType;

  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 7, horizontal: 12),
        margin: const EdgeInsets.only(left: 16, right: 16, bottom: 110),
        decoration: BoxDecoration(
          color: toastType.backgroundColor,
          borderRadius: BorderRadius.circular(8.0),
          border: Border.all(color: toastType.borderColor),
        ),
        child: Row(
          children: [
            SvgPicture.asset(toastType.icon),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                message,
                style: appContext.textTheme.labelLarge
                    ?.copyWith(color: toastType.textColor),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

extension ToastTypeExtension on ToastType {
  Color get textColor {
    switch (this) {
      case ToastType.success:
        return AppColors.darkGreen;
      case ToastType.error:
        return AppColors.darkRed;
      case ToastType.info:
        return AppColors.darkBlue;
      default:
        return Colors.white;
    }
  }

  Color get backgroundColor {
    switch (this) {
      case ToastType.success:
        return AppColors.lightGreen;
      case ToastType.error:
        return AppColors.lightRed;
      case ToastType.info:
        return AppColors.lightBlue;
      default:
        return Colors.grey;
    }
  }

  Color get borderColor {
    switch (this) {
      case ToastType.success:
        return AppColors.green;
      case ToastType.error:
        return AppColors.red;
      case ToastType.info:
        return AppColors.blue;
      default:
        return Colors.grey;
    }
  }

  String get icon {
    switch (this) {
      case ToastType.error:
        return AppIcons.wrong;
      case ToastType.success:
      case ToastType.info:
      case ToastType.warning:
        return AppIcons.check;
    }
  }
}

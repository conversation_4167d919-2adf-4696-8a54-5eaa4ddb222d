import 'dart:developer' as dev;
import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/core/resources/app_icons.dart';
import 'package:zod/core/theme/app_text_theme.dart';
import 'package:zod/features/app/app_providers.dart';
import 'package:zod/features/auction/data/models/auction.dart';
import 'package:zod/features/auction/presentation/providers/auction_countdown_notifier.dart';
import 'dart:math' as math;

/// A widget that displays a "Hurry Up" message when an auction is in its last hour.
///
/// This view is responsible for rendering the UI and hiding itself when the timer ends.
/// The logic for determining whether to initially show this view is handled by the [hurryUpVisibilityProvider].
/// The countdown logic is handled by the [auctionCountdownProvider].
///
/// Features:
/// - Shows a countdown timer that updates in real-time
/// - Prevents the user from navigating back (using WillPopScope and SystemNavigator)
/// - Displays a visual indicator (clock icon) to draw attention
/// - Automatically triggers the auction ended event when the timer reaches zero
/// - Automatically hides itself when the timer ends
///
/// This view is only shown when all of the following conditions are met:
/// 1. The auction has a valid end time
/// 2. The user has joined the auction (checked using participatorIds)
/// 3. The auction is in its last hour (59 minutes and 59 seconds or less remaining)
/// 4. The timer has not ended (countdown.isEnded is false)
class HurryUpView extends ConsumerWidget {
  final Auction auction;

  const HurryUpView({super.key, required this.auction});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final local = ref.read(localProvider);
    // Watch the countdown state
    final countdownState = ref.watch(auctionCountdownProvider(auction));

    // Get the formatted time from the state
    final formattedTime = countdownState.formattedTime;

    // Log that the HurryUpView is being built

    // Parse the formatted time to check if it's completely ended
    final timeParts = formattedTime.split(':');
    final hours = int.tryParse(timeParts[0]) ?? 0;
    final minutes = int.tryParse(timeParts[1]) ?? 0;
    final seconds = int.tryParse(timeParts[2]) ?? 0;

    // We'll check if we should show the view based on time

    // Only hide the view when the timer has completely ended (00:00:00)
    // Keep it visible when there are no minutes but seconds remain (like 00:00:59)
    if (hours == 0 && minutes == 0 && seconds == 0) {
      return const SizedBox.shrink(); // Return an empty widget
    }

    // Prevent back navigation when in last hour
    // This will disable the system back button on Android
    SystemChannels.navigation.invokeMethod('SystemNavigator.preventPopExit');

    // Prevent back navigation using WillPopScope
    return WillPopScope(
      onWillPop: () async {
        // Return false to prevent back navigation
        return false;
      },
      child: Padding(
        padding: const EdgeInsetsDirectional.only(start: 20, end: 16),
        child: Container(
          // Add padding only to left and top, not to right or bottom
          padding: const EdgeInsetsDirectional.only(start: 12, top: 16),
          decoration: BoxDecoration(
            color: AppColors.snowWhite,
            borderRadius: BorderRadiusDirectional.circular(8),
            boxShadow: [
              BoxShadow(
                color: Color.fromRGBO(0, 0, 0, 0.1),
                blurRadius: 12,
                offset: Offset(0, 4),
                spreadRadius: 0,
              ),
            ],
          ),
          child: Stack(
            children: [
              Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // HURRY UP! text
                          Text(
                            context.localizations.hurry_up.toUpperCase(),
                            style:
                                context.textTheme.labelLarge?.charcoalGray.w700,
                          ),
                          const SizedBox(height: 5),

                          // Subtitle
                          Text(
                            context.localizations.auction_end_less_than_hour,
                            style:
                                context.textTheme.labelMedium?.crimsonRed.w700,
                          ),
                          const SizedBox(height: 5),

                          // Timer pill with StreamBuilder
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: AppColors.crimsonRed,
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Text(
                              "${context.localizations.time_left} $formattedTime",
                              style: context.textTheme.labelMedium?.white,
                            ),
                          ),
                        ],
                      ),
                    ),
                    // Add space for the clock icon
                    const SizedBox(width: 60),
                  ],
                ),
              ),
              // Clock icon positioned at end bottom
              PositionedDirectional(
                bottom: 0,
                end: 0,
                child: Transform.flip(
                  flipX: local.languageCode == 'ar' ? true : false,
                  child: ClipRRect(
                    borderRadius: BorderRadiusDirectional.only(
                      bottomEnd: Radius.circular(8),
                    ),
                    child: SvgPicture.asset(
                      AppIcons.hurryUpClock,
                      width: 60,
                      height: 60,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

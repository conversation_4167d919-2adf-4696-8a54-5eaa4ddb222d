import 'dart:math' as math;

import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:share_plus/share_plus.dart';
import 'package:simple_shadow/simple_shadow.dart';
import 'package:zod/app/routes.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/core/resources/app_icons.dart';
import 'package:zod/core/utils/timezone_utils.dart';
import 'package:zod/core/widgets/network_image_widget.dart';
import 'package:zod/features/auction/data/models/auction.dart';
import 'package:zod/features/auction/presentation/providers/auction_provider.dart';
import 'package:zod/features/auction/presentation/providers/open_auctions_provider.dart';
import 'package:zod/features/auction/presentation/providers/switch_joined_open_auction_provider.dart';
import 'package:zod/features/auction/presentation/views/count_down_timer_view.dart';
import 'package:zod/features/home/<USER>/providers/coming_auctions_provider.dart';

final carouselControllerProvider = Provider<CarouselSliderController>((ref) {
  return CarouselSliderController();
});

class AuctionSliverAppBar extends ConsumerWidget {
  final bool isRTL;
  final bool isFavorite;
  final bool fromHome;
  final bool disableBack;
  final VoidCallback? toggleFavorite;
  final Auction? auction;

  const AuctionSliverAppBar(
      {super.key,
      required this.isRTL,
      this.isFavorite = false,
      this.toggleFavorite,
      this.auction,
      this.fromHome = false,
      this.disableBack = false});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentImageIndex = ref.watch(imageIndexProvider);
    final carouselController = ref.watch(carouselControllerProvider);
    return SliverAppBar(
      // 30% from full height
      expandedHeight: MediaQuery.of(context).size.height * 0.3, //30%from
      floating: false,
      pinned: true,
      stretch: true,
      systemOverlayStyle: SystemUiOverlayStyle(
        statusBarColor: AppColors.light,
        statusBarIconBrightness: Brightness.dark,
        statusBarBrightness: Brightness.light,
      ),
      automaticallyImplyLeading: false,
      elevation: 0,
      title: Transform.translate(
        offset: Offset(isRTL ? 20 : -20, 5),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            InkWell(
              highlightColor: Colors.transparent,
              splashColor: Colors.transparent,
              onTap: disableBack ? null : () => _onBackTap(ref),
              child: SimpleShadow(
                color: Colors.black,
                opacity: 0.06,
                offset: Offset(0, 6),
                sigma: 10,
                child: Transform.rotate(
                  angle: isRTL ? math.pi : 0,
                  child: SizedBox(
                      width: 70, child: SvgPicture.asset(AppIcons.backArrow)),
                ),
              ),
            ),
            if ((auction?.isJoined ?? false) &&
                auction?.endedAt != null &&
                !(auction?.isClosed ?? false))
              Padding(
                padding: const EdgeInsets.only(top: 20.0),
                child: CountdownTimerView(
                  title: context.localizations.time_left,
                  targetTime:
                      auction?.endedAt ?? TimezoneUtils.getCurrentLocalTime(),
                ),
              ),
          ],
        ),
      ),
      actions: [
        InkWell(
          onTap: toggleFavorite,
          child: Container(
            padding: EdgeInsetsDirectional.only(
                start: 6.8, end: 5.8, top: 6, bottom: 6),
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(30),
            ),
            child: SvgPicture.asset(AppIcons.save),
          ),
        ),
        SizedBox(width: 8),
        InkWell(
          onTap: () => _onShare(context),
          child: Padding(
            padding: EdgeInsetsDirectional.only(end: 19),
            child: Container(
              padding: EdgeInsetsDirectional.only(
                  start: 6.8, end: 5.8, top: 6, bottom: 6),
              decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: BorderRadius.circular(30),
              ),
              child: SvgPicture.asset(AppIcons.share),
            ),
          ),
        ),
      ],
      flexibleSpace: FlexibleSpaceBar(
        background: Stack(
          fit: StackFit.expand,
          children: [
            CarouselSlider(
              carouselController: carouselController,
              options: CarouselOptions(
                height: 370,
                autoPlay: false,
                viewportFraction: 1.0,
                onPageChanged: (index, reason) {
                  ref.read(imageIndexProvider.notifier).state = index;
                },
              ),
              items: auction?.gallery != null
                  ? auction?.gallery?.map((image) {
                      return Container(
                        decoration: BoxDecoration(),
                        child: NetworkImageView(
                          image,
                          fit: BoxFit.cover,
                          radius: 0,
                          width: double.infinity,
                        ),
                      );
                    }).toList()
                  : [],
            ),
            if (auction?.gallery != null)
              Positioned(
                bottom: 16,
                left: 0,
                right: 0,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: auction!.gallery!.asMap().entries.map((entry) {
                    return Container(
                      width: 8,
                      height: 8,
                      margin: EdgeInsets.symmetric(horizontal: 4),
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: currentImageIndex == entry.key
                            ? AppColors.royalBlue
                            : AppColors.lightGray,
                      ),
                    );
                  }).toList(),
                ),
              ),
          ],
        ),
      ),
    );
  }

  void _onBackTap(WidgetRef ref) {
    ref.invalidate(showSwitchProvider);
    ref.invalidate(openAuctionsProvider(''));
    ref.invalidate(upComingAuctionsProvider);

    if (fromHome) {
      goRouter.pushReplacement('/auctions');
    } else {
      if (goRouter.canPop()) {
        goRouter.pop();
      } else {
        goRouter.go('/home');
      }
    }
  }

  Future<void> _onShare(BuildContext context) async {
    await Share.share(
      '${context.localizations.share_challenge_text} ${auction?.name}\n '
      'https://zod-backend-main-hyrap1.laravel.cloud/${auction?.id}',
      subject:
          "${context.localizations.share_challenge_text}${auction?.name ?? ''}",
    );
  }
}

import 'dart:developer';

import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/features/auction/data/api.dart';
import 'package:zod/features/auction/data/models/auction.dart';

import '../../../../core/list_state.dart';

final closedAuctionsProvider =
    FutureProvider.autoDispose<List<Auction>>((ref) async {
  log("closedAuctionsProvider");
  final api = sl<AuctionApi>();
  final data = await api.closedAuctions();
  return data.data;

});

class ClosedAuctionsController
    extends AutoDisposeAsyncNotifier<ListState<Auction>> {
  ClosedAuctionsController(this._api) : super();

  final AuctionApi _api;
  ScrollController? scrollController;

  @override
  Future<ListState<Auction>> build() async {
    _initialize();
    final response = await _api.closedAuctions();
    log("response ${response.data}");
    return ListState.data(response);
  }

  void _initialize() {
    state = const AsyncValue.loading();
    scrollController = ScrollController();
    scrollController?.addListener(_scrollControllerListener);

    ref.onDispose(
          () {
        scrollController?.dispose();
        scrollController?.removeListener(_scrollControllerListener);
      },
    );
  }

  void _loadMore() async {
    log("next _loadMore");
    final next = state.requireValue.data.links?.next;
    log("next $next");
    if (next == null) return;
    state = AsyncData(state.requireValue.loading());
    state = await AsyncValue.guard(() async {
      final response = await _api.closedAuctions(
        nextUrl: state.requireValue.data.links?.next,
      );
      return state.requireValue.success(response);
    });
  }

  void _scrollControllerListener() {
    if (scrollController == null) return;
    if (scrollController?.hasClients == false) return;

    final maxScroll = scrollController!.position.maxScrollExtent;
    final currentScroll = scrollController!.position.pixels;
    const delta = 200.0;
    if (maxScroll - currentScroll <= delta &&
        state.value?.status != ListStatus.loading) {
      _loadMore();
    }
  }
}
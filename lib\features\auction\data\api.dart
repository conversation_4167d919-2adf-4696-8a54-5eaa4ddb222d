import 'package:bond_network/bond_network.dart';
import 'package:zod/features/auction/data/models/auction.dart';
import 'package:zod/features/auction/data/models/auction_meta.dart';
import 'package:zod/features/auction/data/models/bid_history.dart';
import 'package:zod/features/auction/data/models/bid_history_meta.dart';
import 'package:zod/features/auction/data/models/claim_prize.dart';
import 'package:zod/features/auction/data/models/extra_time_reason_response.dart';
import 'package:zod/features/auction/data/models/payment_result.dart';
import 'package:zod/features/auth/data/errors/validation_error.dart';
import 'package:zod/features/home/<USER>/models/address.dart';
import 'package:zod/features/home/<USER>/models/review.dart';

import '../../home/<USER>/models/winner_auction.dart';

typedef AuctionApiResult = SingleResponse<Auction>;
typedef AuctionApiJoin = SingleMResponse<Auction, AuctionMeta>;
typedef BidApiResult = SingleMResponse<BidHistory, AuctionMeta>;
typedef ClaimPrizeApiResult = SingleResponse<ClaimPrize>;
typedef AddressListApiResult = ListResponse<Address>;
typedef AddressApiResult = SingleResponse<Address>;
typedef ExtraTimeReasonApiResult = SingleResponse<ExtraTimeReasonResponse>;
typedef PaymentApiResult = SingleMResponse<PaymentResult, AuctionMeta>;

class AuctionApi {
  final BondFire _bondFire;

  AuctionApi(this._bondFire);

  Future<ListResponse<Auction>> openAuctions(
          {String? nextUrl, String? userId}) =>
      _bondFire
          .get<ListResponse<Auction>>(
              nextUrl ?? '/opened-auctions?joined_id=$userId')
          .cache(cachePolicy: CachePolicy.networkElseCache)
          .header(Api.headers())
          .factory(ListResponse<Auction>.fromJson)
          .errorFactory(ServerError.fromJson)
          .execute();

  Future<ListResponse<Auction>> upComingAuctions({String? nextUrl}) => _bondFire
      .get<ListResponse<Auction>>(nextUrl ?? '/upcoming-auctions')
      .cache(cachePolicy: CachePolicy.networkElseCache)
      .header(Api.headers())
      .factory(ListResponse<Auction>.fromJson)
      .errorFactory(ServerError.fromJson)
      .execute();

  Future<ListResponse<WinnerAuction>> winnerAuctions({String? nextUrl}) =>
      _bondFire
          .get<ListResponse<WinnerAuction>>(nextUrl ?? '/winner-auctions')
          .cache(cachePolicy: CachePolicy.networkElseCache)
          .header(Api.headers())
          .factory(ListResponse<WinnerAuction>.fromJson)
          .errorFactory(ServerError.fromJson)
          .execute();

  Future<ListResponse<Review>> winnerReviewsAuctions({String? nextUrl}) =>
      _bondFire
          .get<ListResponse<Review>>(nextUrl ?? '/auctions/reviews')
          .cache(cachePolicy: CachePolicy.networkElseCache)
          .header(Api.headers())
          .factory(ListResponse<Review>.fromJson)
          .errorFactory(ServerError.fromJson)
          .execute();

  Future<ListResponse<Auction>> closedAuctions({String? nextUrl}) async =>
      _bondFire
          .get<ListResponse<Auction>>(nextUrl ?? '/closed-auctions')
          .cache(cachePolicy: CachePolicy.networkElseCache)
          .header(Api.headers())
          .factory(ListResponse<Auction>.fromJson)
          .errorFactory(ServerError.fromJson)
          .execute();

  Future<AuctionApiJoin> auction(int id) => _bondFire
      .get<AuctionApiJoin>('/auctions/$id')
      .cache(cachePolicy: CachePolicy.networkElseCache)
      .header(Api.headers())
      .factory(AuctionApiJoin.fromJson)
      .errorFactory(ServerError.fromJson)
      .execute();

  Future<Map<String, dynamic>> favorite(int id) => _bondFire
      .post<Map<String, dynamic>>('/auctions/$id/favorite')
      .header(Api.headers())
      .factory(Map<String, dynamic>.from)
      .errorFactory(ServerError.fromJson)
      .execute();

  Future<AuctionApiJoin> join(int auctionId) => _bondFire
      .post<AuctionApiJoin>('/auction/$auctionId/join')
      .header(Api.headers())
      .factory(AuctionApiJoin.fromJson)
      .errorFactory(ServerError.fromJson)
      .execute();

  Future<AuctionApiJoin> placeBid(int auctionId, int bidAmount) => _bondFire
      .post<AuctionApiJoin>('/auction/$auctionId/bid')
      .body({'bid_amount': bidAmount})
      .header(Api.headers())
      .factory(AuctionApiJoin.fromJson)
      .errorFactory(ServerError.fromJson)
      .execute();

  Future<ListMResponse<BidHistory, BidHistoryMeta>> getBids(int auctionId,
          {String? nextUrl}) =>
      _bondFire
          .get<ListMResponse<BidHistory, BidHistoryMeta>>(
              nextUrl ?? '/auction/$auctionId/bids')
          .cache(cachePolicy: CachePolicy.networkOnly)
          .header(Api.headers())
          .factory(ListMResponse<BidHistory, BidHistoryMeta>.fromJson)
          .errorFactory(ServerError.fromJson)
          .execute();

  /// Trigger the auction ended event when the hurry up timer finishes
  Future<Map<String, dynamic>> triggerAuctionEnded(int auctionId) => _bondFire
      .post<Map<String, dynamic>>('/auctions/$auctionId/trigger-ended')
      .header(Api.headers())
      .factory(Map<String, dynamic>.from)
      .errorFactory(ServerError.fromJson)
      .execute();

  /// Get claim prize data for a specific auction
  Future<ClaimPrizeApiResult> getClaimPrizeData(int auctionId) => _bondFire
      .get<ClaimPrizeApiResult>('/auctions/$auctionId/prize')
      .cache(cachePolicy: CachePolicy.networkOnly)
      .header(Api.headers())
      .factory(ClaimPrizeApiResult.fromJson)
      .errorFactory(ServerError.fromJson)
      .execute();

  /// Get user addresses
  Future<AddressListApiResult> getUserAddresses() => _bondFire
      .get<AddressListApiResult>('/addresses')
      .cache(cachePolicy: CachePolicy.networkElseCache)
      .header(Api.headers())
      .factory(AddressListApiResult.fromJson)
      .errorFactory(ServerError.fromJson)
      .execute();

  /// Add a new address
  Future<AddressApiResult> addAddress(Map<String, dynamic> addressData) =>
      _bondFire
          .post<AddressApiResult>('/addresses')
          .body(addressData)
          .header(Api.headers())
          .factory(AddressApiResult.fromJson)
          .errorFactory(ValidationError.fromJson)
          .execute();

  /// Request extra time for claiming prize
  Future<ClaimPrizeApiResult> requestExtraTime(int auctionId) => _bondFire
      .post<ClaimPrizeApiResult>('/auctions/$auctionId/request-extra-time')
      .header(Api.headers())
      .factory(ClaimPrizeApiResult.fromJson)
      .errorFactory(ServerError.fromJson)
      .execute();

  /// Process payment for claimed prize
  Future<PaymentApiResult> processPayment(int auctionId, int addressId) =>
      _bondFire
          .post<PaymentApiResult>('/auctions/$auctionId/process-payment')
          .body({'address_id': addressId})
          .header(Api.headers())
          .factory(PaymentApiResult.fromJson)
          .errorFactory(ServerError.fromJson)
          .execute();
  Future<ExtraTimeReasonApiResult> reasons() => _bondFire
      .get<ExtraTimeReasonApiResult>('/time-extension/reasons')
      .cache(cachePolicy: CachePolicy.networkElseCache)
      .header(Api.headers())
      .factory(ExtraTimeReasonApiResult.fromJson)
      .errorFactory(ValidationError.fromJson)
      .execute();

  Future<Map<String, dynamic>> requestExtraTimeReason(
          Map<String, dynamic> body) =>
      _bondFire
          .post<Map<String, dynamic>>('/time-extension/request')
          .body(body)
          .header(Api.headers())
          .factory(Map<String, dynamic>.from)
          .errorFactory(ValidationError.fromJson)
          .execute();

  /// Toggle reminder for an auction
  Future<Map<String, dynamic>> toggleReminder(int auctionId) => _bondFire
      .post<Map<String, dynamic>>('/auction/$auctionId/toggle-reminder')
      .header(Api.headers())
      .factory(Map<String, dynamic>.from)
      .errorFactory(ServerError.fromJson)
      .execute();
}

import 'dart:convert';

import 'package:bond_notifications/bond_notifications.dart';
import 'package:zod/app/routes.dart';
import 'package:zod/features/auction/presentation/providers/auction_provider.dart';

class AuctionStartedPushNotifications extends PushNotification
    with ActionablePushNotification {
  @override
  List<String> get code => [
        'auction_started',
        'order_update_delivery_status',
        'auction_capacity_reached'
      ];

  @override
  void onNotification(NotificationData data) {
    auctionStreamControllerRefresh.add(data);
  }

  @override
  void onNotificationTapped(NotificationData data) {
    try {
      // Check if data contains auction_id directly
      if (data.containsKey("auction_id")) {
        final auctionId = data["auction_id"];
        goRouter.push('/auction/$auctionId');
        return;
      }

      // If not, try to parse the data['data'] as JSON
      if (data.containsKey("data")) {
        final dataString = data['data'] as String;

        // Parse the JSON string to get the auction_id
        final dataMap = jsonDecode(dataString);
        final auctionId = dataMap['auction_id'];

        if (auctionId != null) {
          goRouter.push('/auction/$auctionId');
          return;
        }
      }

      // No auction_id found in notification data
    } catch (e) {
      // Error handled silently
    }
  }
}

// ignore_for_file: avoid_renaming_method_parameters

import 'dart:developer';

import 'package:bond_core/bond_core.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../data/api.dart';
import '../../data/events/interest_event_tracker.dart';

class AuctionFavoriteNotifier
    extends AutoDisposeFamilyNotifier<Map<String, dynamic>, int> {
  @override
  Map<String, dynamic> build(int id) => {};

  Future<void> toggleFavorite(int auctionId, {String auctionName = ''}) async {
    final api = sl<AuctionApi>();
    try {
      final response = await api.favorite(auctionId);
      log("response auctionFavoriteNotifier $response");

      // Track the interest event
  

      state = response;
    } catch (e) {
      log("Error in toggleFavorite: $e");
      rethrow;
    }
  }
}

final auctionFavoriteProvider = NotifierProvider.autoDispose
    .family<AuctionFavoriteNotifier, Map<String, dynamic>, int>(
  () => AuctionFavoriteNotifier(),
);

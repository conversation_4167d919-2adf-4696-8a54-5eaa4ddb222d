import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/features/auction/data/models/claim_prize.dart';
import 'package:zod/features/auction/presentation/providers/claim_prize_provider.dart';
import 'package:zod/features/auction/presentation/providers/timer_provider.dart';

class ExtraTimeRequestView extends ConsumerWidget {
  final int auctionId;
  final ClaimPrize claimPrize;
  final CountdownTimerState timerState;

  const ExtraTimeRequestView({
    super.key,
    required this.auctionId,
    required this.claimPrize,
    required this.timerState,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (!timerState.isVisible) {
      return const SizedBox.shrink();
    }
    final claimPrizeState = ref.watch(claimPrizeProvider(auctionId));
    final isRequestingExtraTime = claimPrizeState.isRequestingExtraTime;

    return Padding(
      padding: const EdgeInsets.only(top: 4),
      child: Center(
        child: isRequestingExtraTime
            ? const CircularProgressIndicator.adaptive(strokeWidth: 2)
            : RichText(
                text: TextSpan(
                  style: context.textTheme.bodySmall,
                  children: [
                    TextSpan(
                        text: context.localizations.request_extra_time_prefix),
                    TextSpan(
                      text: context.localizations.request_extra_time_bold,
                      style: const TextStyle(
                        fontWeight: FontWeight.w700,
                        decoration: TextDecoration.underline,
                      ),
                    ),
                    const WidgetSpan(
                      child: SizedBox(
                        width: 2,
                      ),
                    ),
                    TextSpan(
                        text: context.localizations.request_extra_time_suffix),
                  ],
                ),
              ),
      ),
    );
  }
}

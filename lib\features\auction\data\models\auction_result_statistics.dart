import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:bond_core/bond_core.dart';

part 'auction_result_statistics.g.dart';

@JsonSerializable(explicitToJson: true)
class AuctionResultStatistics extends Equatable with Jsonable {
  @Json<PERSON>ey(name: 'total_bids')
  final int totalBids;
  
  @<PERSON>sonKey(name: 'total_bidders')
  final int totalBidders;
  
  @JsonKey(name: 'winner_bids')
  final int winnerBids;

  const AuctionResultStatistics({
    required this.totalBids,
    required this.totalBidders,
    required this.winnerBids,
  });

  factory AuctionResultStatistics.fromJson(Map<String, dynamic> json) =>
      _$AuctionResultStatisticsFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$AuctionResultStatisticsToJson(this);

  @override
  List<Object?> get props => [totalBids, totalBidders, winnerBids];
}

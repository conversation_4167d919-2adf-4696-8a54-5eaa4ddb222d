import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/app_theme.dart';
import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/core/widgets/network_image_widget.dart';
import 'package:zod/features/auction/data/models/claim_prize.dart';
import 'package:zod/features/auction/presentation/providers/timer_provider.dart';

class ProductCardView extends StatelessWidget {
  final ClaimPrize claimPrize;
  final CountdownTimerState timerState;

  const ProductCardView({
    super.key,
    required this.claimPrize,
    required this.timerState,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppColors.athensGray),
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              children: [
                NetworkImageView(
                  claimPrize.image,
                  width: 60,
                  height: 60,
                ),
                const SizedBox(width: 16),
                // Product details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        claimPrize.name,
                        style: context.textTheme.titleSmall,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          // Timer bar
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(16),
                bottomRight: Radius.circular(16),
              ),
              gradient: LinearGradient(
                end: AlignmentDirectional.centerStart,
                begin: AlignmentDirectional.centerEnd,
                stops: [0.70, 1],
                colors: [
                  Color.fromARGB(255, 208, 211, 255),
                  Color.fromARGB(255, 255, 255, 255),
                ],
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  context.localizations.time_left_to_claim_prize,
                  style: context.textTheme.bodyMedium,
                ),
                Text(
                  timerState.formatDuration,
                  style: context.textTheme.titleSmall?.w700,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

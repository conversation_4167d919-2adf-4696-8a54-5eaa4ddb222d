import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/app_theme.dart';

import 'package:zod/features/auction/data/models/auction.dart';
import 'package:zod/features/auction/presentation/views/joined_view.dart';
import '../../../../core/widgets/number_coins_view.dart';
import 'auction_status_label.dart';
import 'count_down_timer_view.dart';

class HeaderAuctionItemView extends StatelessWidget {
  const HeaderAuctionItemView({super.key, required this.auction});

  final Auction auction;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(left: 12, right: 12, top: 10, bottom: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          auction.showBidSummary
              ? Row(
            children: [
              Text(
                context.localizations.total_bid,
                style: context.textTheme.labelMedium,
              ),
              const SizedBox(width: 6),
              Text(
                '|',
                style: context.textTheme.labelMedium?.lightGray,
              ),
              const SizedBox(width: 6),
              Text(
                auction.totalBidPoints.toString(),
                style: context.textTheme.labelMedium?.w700,
              ),
            ],
          )
              : Row(
            children: [
              Text(
                context.localizations.start_bidding,
                style: context.textTheme.labelMedium,
              ),
              const SizedBox(width: 4),
              NumberCoinsView(number: auction.openBidPoints.toString()),
            ],
          ),
          Row(
            children: [
              if (auction.showEndCountdown)
                Skeleton.ignore(
                  child: CountdownTimerView(
                    title: context.localizations.time_left,
                    targetTime: auction.endedAt!,
                  ),
                ),
              if (auction.showStartCountdown)
                Skeleton.ignore(
                  child: CountdownTimerView(
                    title: context.localizations.start_in,
                    targetTime: auction.startAt!,
                  ),
                ),
              const SizedBox(width: 4),
              if (auction.showClosedJoinedView) const JoinedView(),
              const SizedBox(width: 4),
              if (auction.showAuctionStatusLabel)
                AuctionStatusLabel(status: auction.status),
            ],
          ),
        ],
      ),
    );
  }

}

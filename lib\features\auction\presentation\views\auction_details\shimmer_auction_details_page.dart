import 'package:flutter/material.dart';
import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/features/auction/presentation/views/auction_details/shimmer_auction_details_view.dart';
import 'package:zod/features/auction/presentation/views/auction_details/shimmer_auction_tabs_view.dart';

class ShimmerAuctionDetailsPage extends StatelessWidget {
  const ShimmerAuctionDetailsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return NestedScrollView(
      headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
        return [
          SliverAppBar(
            expandedHeight: 250,
            floating: false,
            pinned: true,
            stretch: true,
            backgroundColor: AppColors.lightGray,
          ),
        ];
      },
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ShimmerAuctionDetailsView(),
          ShimmerAuctionTabsView(),
        ],
      ),
    );
  }
}

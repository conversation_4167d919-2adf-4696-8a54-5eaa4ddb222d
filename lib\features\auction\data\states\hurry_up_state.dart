import 'package:flutter/material.dart';
import 'package:zod/features/auction/data/models/auction.dart';

/// State for the HurryUpView
///
/// This class holds the data needed to render the HurryUpView
class HurryUpState {
  /// The auction associated with this view
  final Auction auction;
  
  /// The remaining time formatted as a string
  final String formattedRemainingTime;
  
  /// Whether the view should be visible
  final bool isVisible;
  
  /// Constructor
  const HurryUpState({
    required this.auction,
    required this.formattedRemainingTime,
    required this.isVisible,
  });
  
  /// Creates a copy of this state with the specified fields replaced
  HurryUpState copyWith({
    Auction? auction,
    String? formattedRemainingTime,
    bool? isVisible,
  }) {
    return HurryUpState(
      auction: auction ?? this.auction,
      formattedRemainingTime: formattedRemainingTime ?? this.formattedRemainingTime,
      isVisible: isVisible ?? this.isVisible,
    );
  }
  
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is HurryUpState &&
          runtimeType == other.runtimeType &&
          auction.id == other.auction.id &&
          formattedRemainingTime == other.formattedRemainingTime &&
          isVisible == other.isVisible;

  @override
  int get hashCode => 
      auction.id.hashCode ^ 
      formattedRemainingTime.hashCode ^ 
      isVisible.hashCode;
}

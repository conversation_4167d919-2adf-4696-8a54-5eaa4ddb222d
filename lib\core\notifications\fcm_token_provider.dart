import 'package:bond_network/bond_network.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:get_it/get_it.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:zod/core/notifications/fcm_token_api.dart';
import 'package:zod/core/notifications/firebase_messaging_provider.dart';
import 'package:zod/core/notifications/push_notifications_providers/firebase_messaging_push_notification_provider.dart';

final deviceInfoProvider = Provider<DeviceInfoPlugin>((ref) {
  return DeviceInfoPlugin();
});

final packageInfoProvider = FutureProvider<PackageInfo>((ref) async {
  return await PackageInfo.fromPlatform();
});

final fcmTokenApiProvider = Provider<FcmTokenApi>((ref) {
  final bondFire = GetIt.I<BondFire>();
  final deviceInfo = ref.watch(deviceInfoProvider);
  // We'll initialize PackageInfo in the service
  final packageInfo =
      PackageInfo(appName: '', packageName: '', version: '', buildNumber: '');

  return FcmTokenApi(bondFire, deviceInfo, packageInfo);
});

final fcmTokenProvider = Provider<FcmTokenService>((ref) {
  final fcmTokenApi = ref.watch(fcmTokenApiProvider);
  final firebaseMessaging = ref.watch(firebaseMessagingProvider);

  return FcmTokenService(fcmTokenApi, firebaseMessaging);
});

class FcmTokenService {
  final FcmTokenApi _fcmTokenApi;
  final FirebaseMessagingNotificationProvider _firebaseMessaging;

  FcmTokenService(this._fcmTokenApi, this._firebaseMessaging);

  /// Initialize FCM token handling
  Future<void> initialize() async {
    // Get the current token and send it to the server
    final token = await _firebaseMessaging.token;
    if (token != null) {
      await _fcmTokenApi.updateToken(token);
    }

    // Listen for token refreshes
    _firebaseMessaging.onTokenRefresh.listen((newToken) {
      _fcmTokenApi.updateToken(newToken);
    });
  }
}

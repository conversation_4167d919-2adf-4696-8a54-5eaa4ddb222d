import 'dart:developer';

import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/core/resources/app_icons.dart';
import 'package:zod/core/utils/custom_marker_helper.dart';
import 'package:zod/features/home/<USER>/models/address.dart';

class MiniMapView extends StatefulWidget {
  final Address address;
  final VoidCallback? onTap;

  const MiniMapView({
    super.key,
    required this.address,
    this.onTap,
  });

  @override
  State<MiniMapView> createState() => _MiniMapViewState();
}

class _MiniMapViewState extends State<MiniMapView> {
  GoogleMapController? _mapController;
  Set<Marker> _markers = {};
  Address? _previousAddress;

  @override
  void initState() {
    super.initState();
    _previousAddress = widget.address;
    _initializeMarkers();
  }

  @override
  void didUpdateWidget(MiniMapView oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Check if the address has changed (especially latitude and longitude)
    if (widget.address.latitude != _previousAddress?.latitude ||
        widget.address.longitude != _previousAddress?.longitude) {
      log('MiniMapView: Address changed from ${_previousAddress?.latitude},${_previousAddress?.longitude} to ${widget.address.latitude},${widget.address.longitude}');
      _previousAddress = widget.address;
      _initializeMarkers();
      _updateCameraPosition();
    }
  }

  @override
  void dispose() {
    _mapController?.dispose();
    super.dispose();
  }

  Future<void> _initializeMarkers() async {
    if (widget.address.latitude == null || widget.address.longitude == null) {
      log('MiniMapView: Cannot initialize markers - latitude or longitude is null');
      return;
    }
    log('MiniMapView: Initializing markers with lat=${widget.address.latitude}, lng=${widget.address.longitude}');

    final position = LatLng(
      double.parse(widget.address.latitude!),
      double.parse(widget.address.longitude!),
    );

    final bitmapDescriptor =
        await BitmapDescriptorHelper.getBitmapDescriptorFromSvgAsset(
      'assets/icons/map_pin.svg',
    );

    setState(() {
      _markers = {
        Marker(
          markerId: const MarkerId('address_location'),
          position: position,
          icon: bitmapDescriptor,
          visible: true,
          zIndex: 2,
        ),
      };
    });
  }

  void _updateCameraPosition() {
    if (_mapController == null ||
        widget.address.latitude == null ||
        widget.address.longitude == null) {
      log('MiniMapView: Cannot update camera position - controller or coordinates are null');
      return;
    }
    log('MiniMapView: Updating camera position to lat=${widget.address.latitude}, lng=${widget.address.longitude}');

    final position = LatLng(
      double.parse(widget.address.latitude!),
      double.parse(widget.address.longitude!),
    );

    _mapController!.animateCamera(
      CameraUpdate.newLatLngZoom(position, 15),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Default location for Saudi Arabia (Riyadh)
    final defaultLocation = const LatLng(24.7136, 46.6753);

    // Use address coordinates if available, otherwise use default
    final LatLng location =
        (widget.address.latitude != null && widget.address.longitude != null)
            ? LatLng(
                double.parse(widget.address.latitude!),
                double.parse(widget.address.longitude!),
              )
            : defaultLocation;

    return GestureDetector(
      onTap: widget.onTap,
      child: Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: AppColors.lightGray),
          color: AppColors.white,
        ),
        child: Column(
          children: [
            SizedBox(
              height: 100,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(16),
                child: GoogleMap(
                  initialCameraPosition: CameraPosition(
                    target: location,
                    zoom: 15,
                  ),
                  markers: _markers,
                  zoomControlsEnabled: false,
                  mapToolbarEnabled: false,
                  myLocationButtonEnabled: false,
                  compassEnabled: false,
                  mapType: MapType.normal,
                  onMapCreated: (GoogleMapController controller) {
                    _mapController = controller;
                    // Ensure camera position is updated when map is created
                    _updateCameraPosition();
                  },
                  // Disable all interactions
                  scrollGesturesEnabled: false,
                  zoomGesturesEnabled: false,
                  tiltGesturesEnabled: false,
                  rotateGesturesEnabled: false,
                ),
              ),
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    SvgPicture.asset(AppIcons.gps),
                    const SizedBox(width: 8),
                    SizedBox(
                      width: 210, // العرض اللي تريده مثلاً 180 بكسل
                      child: Text(
                        widget.address.toString(),
                        style: context.textTheme.bodySmall,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        softWrap: true,
                      ),
                    ),
                  ],
                ),
                SvgPicture.asset(AppIcons.editCircular),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:zod/app/routes.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/app_theme.dart';
import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/core/resources/app_icons.dart';
import 'package:zod/features/auction/presentation/add_address_page.dart';
import 'package:zod/features/auction/presentation/providers/claim_prize_provider.dart';
import 'package:zod/features/auction/presentation/providers/selected_address_provider.dart';
import 'package:zod/features/auction/presentation/select_address_view.dart';
import 'package:zod/features/auction/presentation/views/mini_map_view.dart';
import 'package:zod/features/auth/auth.dart';
import 'package:zod/features/auth/presentation/providers/phone_number_validation_provider.dart';
import 'package:zod/features/home/<USER>/models/address.dart';

class DeliveryAddressSectionView extends ConsumerWidget {
  final int auctionId;
  final Address? selectedAddress;

  const DeliveryAddressSectionView({
    super.key,
    required this.auctionId,
    this.selectedAddress,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final hasSelectedAddress = selectedAddress != null;

    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Text(
        context.localizations.delivery_address,
        style: context.textTheme.titleSmall?.w700,
      ),
      Text(
        context.localizations.delivery_address_description,
        style: context.textTheme.bodySmall,
      ),
      const SizedBox(height: 12),
      if (hasSelectedAddress)
        // Show selected address with mini map
        Column(
          children: [
            // Mini Map
            MiniMapView(
              address: selectedAddress!,
              onTap: () {
                ref.invalidate(phoneNumberValidationProvider);
                (Auth.check() && Auth.user().hasAddress != true)
                    ? goRouter.push(AddAddressPage.route, extra: auctionId)
                    : _showAddressSelectionBottomSheet(context, ref);
              },
            ),
            const SizedBox(height: 12),
            // Formatted Address
          ],
        )
      else
        // Show add address button
        InkWell(
          onTap: () => _addOrSelectAddress(ref, context),
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.disabled,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppColors.softSteel),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SvgPicture.asset(AppIcons.add),
                const SizedBox(width: 6),
                Text(
                  context.localizations.add_address,
                  style: context.textTheme.labelLarge?.steelGray,
                ),
              ],
            ),
          ),
        ),
    ]);
  }

  void _addOrSelectAddress(WidgetRef ref, BuildContext context) {
    ref.invalidate(phoneNumberValidationProvider);
    (Auth.check() && Auth.user().hasAddress != true)
        ? goRouter.push(AddAddressPage.route, extra: auctionId)
        : _showAddressSelectionBottomSheet(context, ref);
  }

  void _showAddressSelectionBottomSheet(BuildContext context, WidgetRef ref) {
    if (selectedAddress != null) {
      ref
          .read(selectedAddressProvider.notifier)
          .selectAddress(selectedAddress!);
    }
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => SelectAddressPage(
        onAddressSelected: (address) {
          // Update the selected address in the provider
          ref
              .read(claimPrizeProvider(auctionId).notifier)
              .selectAddress(address);

          // Close the bottom sheet
          goRouter.pop(context); // Use Navigator.pop for modal bottom sheets
        },
        onAddNewAddressPressed: () async {
          // Close the bottom sheet
          goRouter.pop(context); // Use Navigator.pop for modal bottom sheets

          // Navigate to the add address page using GoRouter
          // Use push with the correct route path to ensure we can return to the claim prize page
          final result = await goRouter.push<Address>(AddAddressPage.route);

          // Update the selected address in the provider
          if (result != null) {
            ref
                .read(claimPrizeProvider(auctionId).notifier)
                .selectAddress(result);
          }
        },
      ),
    );
  }
}

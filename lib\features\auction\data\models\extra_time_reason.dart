import 'package:bond_network/bond_network.dart';
import 'package:json_annotation/json_annotation.dart';

part 'extra_time_reason.g.dart';

@JsonSerializable(explicitToJson: true)
class ExtraTimeReason extends Model {
  final String name;

  const ExtraTimeReason({
    required super.id,
    required this.name,
  });

  factory ExtraTimeReason.fromJson(Map<String, dynamic> json) =>
      _$ExtraTimeReasonFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$ExtraTimeReasonToJson(this);

  @override
  List<Object?> get props => [name];
}

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/features/auction/data/models/auction.dart';
import 'package:zod/features/auction/data/states/hurry_up_state.dart';
import 'package:zod/features/auction/presentation/providers/timer_provider.dart';

/// Provider that returns the state for the HurryUpView
final hurryUpStateProvider =
    Provider.family<HurryUpState, Auction>((ref, auction) {
  // Check if the HurryUpView should be visible
  // 1. The user must have joined the auction
  // 2. The auction must be in its last hour
  final hasJoined = auction.hasCurrentUserJoined;
  final isInLastHour = auction.isInLastHour;
  final isVisible = hasJoined && isInLastHour;

  // If the view is not visible or the auction has no end time, return a default state
  if (!isVisible || auction.endedAt == null) {
    return HurryUpState(
      auction: auction,
      formattedRemainingTime: "00:00:00",
      isVisible: false,
    );
  }

  // Create the timer parameters with the auction's end time
  final params = (endTime: auction.endedAt!, auctionId: auction.id);

  // Watch the timer state
  final timerState = ref.watch(auctionCountdownTimerProvider(params));

  // Format the remaining time
  final formattedRemainingTime = ref
      .read(auctionCountdownTimerProvider(params).notifier)
      .formatDuration(timerState.remainingTime);

  return HurryUpState(
    auction: auction,
    formattedRemainingTime: formattedRemainingTime,
    isVisible: isVisible,
  );
});

// ignore_for_file: sort_child_properties_last

import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:zod/app/routes.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/app_theme.dart';
import 'package:zod/core/app_widgets.dart';
import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/core/resources/app_icons.dart';

import 'package:zod/features/auction/data/models/auction.dart';
import 'package:zod/features/auction/data/states/auction_state.dart';
import 'package:zod/features/auction/presentation/providers/auction_provider.dart';
import 'package:zod/features/auction/presentation/providers/timer_provider.dart';
import 'package:zod/features/auth/auth.dart';

class BidPlacementView extends ConsumerWidget {
  final Auction auction;

  const BidPlacementView({
    super.key,
    required this.auction,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final auctionState = ref.watch(auctionProvider(auction.id));
    final auctionNotifier = ref.read(auctionProvider(auction.id).notifier);
    final providerTimerBreakUntil =
        countdownTimerProvider(auctionState.breakUntil ?? DateTime.now());
    final timerBreakUntilState = ref.watch(providerTimerBreakUntil);

    print(
        "Need to Brack >>>  ${auctionState.breakUntil} ${auctionState.needToBrack}");
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Error message
          if (auctionState.bidError?.isNotEmpty ?? false)
            Container(
              margin: const EdgeInsets.only(bottom: 16),
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(Icons.error_outline, color: Colors.red),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      auctionState.bidError!,
                      style: TextStyle(color: Colors.red),
                    ),
                  ),
                  //close error
                  InkWell(
                    onTap: () {
                      auctionNotifier.clearErrors();
                    },
                    child: Icon(
                      Icons.close,
                      color: Colors.red,
                    ),
                  ),
                ],
              ),
            ),

          // Success message
          if (auctionState.bidSuccess)
            Container(
              margin: const EdgeInsets.only(bottom: 16),
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.green.shade50,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(Icons.check_circle_outline, color: Colors.green),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      context.localizations.bid_placed_successfully,
                      style: TextStyle(color: Colors.green),
                    ),
                  ),
                ],
              ),
            ),

          // Suggested bid increments
          Text(
            context.localizations.suggested_bid_increments,
            style: Theme.of(context).textTheme.bodyMedium?.medium,
          ),
          const SizedBox(height: 12),

          // Bid increment buttons
          Row(
            //horizinta lignment
            children: (auction.suggestionBids ?? []).map((bidAmount) {
              return Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 4.0),
                  child: InkWell(
                    onTap: () {
                      auctionNotifier.setBidAmount(bidAmount.toInt());

                      if ((Auth.user().availablePoint) < bidAmount) {
                        var url =
                            "/charge_wallet/${auction.id}/${auction.entryFee}/${auction.incrementBidPoints}";
                        goRouter.push(url, extra: auction);
                      } else {
                        auctionNotifier.setBidAmount(bidAmount.toInt());
                      }
                    },
                    child: Stack(
                      clipBehavior: Clip.none,
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 12,
                          ),
                          decoration: BoxDecoration(
                            color: bidAmount == auctionState.bidAmount
                                ? AppColors.primaryColor
                                : AppColors.softWhite,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: bidAmount == auctionState.bidAmount
                                  ? Colors.transparent
                                  : AppColors.softSteel,
                              width: 1,
                            ),
                          ),
                          child: Row(
                            children: [
                              SvgPicture.asset(
                                AppIcons.coin,
                              ),
                              const SizedBox(width: 3),
                              Text(
                                bidAmount.toInt().toString(),
                                style: bidAmount == auctionState.bidAmount
                                    ? Theme.of(context)
                                        .textTheme
                                        .bodyMedium
                                        ?.white
                                        .medium
                                    : Theme.of(context)
                                        .textTheme
                                        .bodyMedium
                                        ?.slateGray
                                        .medium,
                              ),
                            ],
                          ),
                        ),

                        //
                        if ((Auth.user().availablePoint) < bidAmount)
                          Positioned.directional(
                            textDirection: Directionality.of(context),
                            child: SvgPicture.asset(
                              AppIcons.alertInfo,
                              width: 15,
                              // height: 10,
                            ),
                            end: 5,
                            top: 5,
                          )
                      ],
                    ),
                  ),
                ),
              );
            }).toList(),
          ),

          const SizedBox(height: 16),

          AppButton(
            loading: auctionState.isPlacingBid,
            enabled: auctionState.bidAmount > 0,
            backgroundColor:
                _getButtonBackgroundColor(auctionState, timerBreakUntilState),
            title: _getButtonTitle(
              auctionState,
              timerBreakUntilState,
              context,
            ),
            onPressed: () => _handleButtonPress(
              auctionState,
              auctionState.bidAmount,
              auction,
              auctionNotifier,
              ref,
            ),
          ),
          SizedBox(height: 8),
          if (auction.lowPointsWarning)
            InkWell(
              onTap: () {
                var url =
                    "/charge_wallet/${auction.id}/${auction.entryFee}/${auction.incrementBidPoints}";
                goRouter.push(url, extra: auction);
              },
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                decoration: BoxDecoration(
                  color: AppColors.redBackground,
                  borderRadius: BorderRadius.circular(5),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text.rich(
                      TextSpan(
                        text: context.localizations.only,
                        style: context.textTheme.bodySmall!.w400,
                        children: <InlineSpan>[
                          TextSpan(
                            text: " ",
                          ),
                          WidgetSpan(
                              child: Transform.translate(
                            offset: Offset(0, -2),
                            child: SvgPicture.asset(AppIcons.coin),
                          )),
                          TextSpan(
                            text: " ${Auth.user().availablePoint}",
                            style: context.textTheme.bodySmall!.w700,
                          ),
                          TextSpan(
                            text: " ",
                          ),
                          TextSpan(
                            text: context.localizations.left,
                            style: context.textTheme.bodySmall!.w400,
                          ),
                          TextSpan(
                            text: " ",
                          ),
                          TextSpan(
                            text: context.localizations.buyPoints,
                            style: context.textTheme.bodySmall!.w700.underLine,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          if (!auction.lowPointsWarning)
            InkWell(
              onTap: () {
                var url =
                    "/charge_wallet/${auction.id}/${auction.entryFee}/${auction.incrementBidPoints}";
                goRouter.push(url, extra: auction);
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(context.localizations.you_have,
                      style: context.textTheme.bodySmall),
                  SvgPicture.asset(AppIcons.coin),
                  Text(" ${Auth.check() ? Auth.user().availablePoint : 0}",
                      style: context.textTheme.bodySmall?.w700),
                ],
              ),
            )
          // Place bid button
        ],
      ),
    );
  }

  // Helper Methods
  Color _getButtonBackgroundColor(
      AuctionState state, CountdownTimerState timerState) {
    // if (state.needToBrack) return AppColors.progressBarColor;
    if (state.isMyBidHeights && (!timerState.isInRemaining)) {
      return AppColors.progressBarColor;
    }
    if (state.needReclaimNow) {
      return AppColors.redButton;
    }

    return AppColors.primaryColor;
  }

  String _getButtonTitle(
    AuctionState state,
    CountdownTimerState timerState,
    BuildContext context,
  ) {
    if (state.isMyBidHeights && !timerState.isInRemaining) {
      return "${context.localizations.takeAShortBreak}      ${timerState.formatDuration} s";
    }
    if (state.needReclaimNow) {
      return context.localizations.reclaimYourLeadNow;
    }
    if (state.bids.isEmpty) {
      return context.localizations.placeTheFirstBid;
    }

    if (timerState.isInRemaining) {
      return context.localizations.place_custom_bid;
    }

    return "${context.localizations.takeAShortBreak}      ${timerState.formatDuration} s";
  }

  void _handleButtonPress(
    AuctionState state,
    int bidAmount,
    Auction auction,
    AuctionNotifier auctionNotifier,
    WidgetRef ref,
  ) {
    if (state.needToBrack) return;

    if (Auth.user().availablePoint < bidAmount) {
      final url =
          "/charge_wallet/${auction.id}/${auction.entryFee}/${auction.incrementBidPoints}";
      goRouter.push(url, extra: auction);
      return;
    }

    auctionNotifier.placeBid(ref);
  }
}

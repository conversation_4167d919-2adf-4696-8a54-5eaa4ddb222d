import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:zod/core/app_theme.dart';
import 'package:zod/core/resources/app_icons.dart';

class SummaryRowView extends StatelessWidget {
  final String label;
  final String value;
  final bool isTotal;

  const SummaryRowView({
    super.key,
    required this.label,
    required this.value,
    this.isTotal = false,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: isTotal
                ? context.textTheme.bodyMedium?.w700
                : context.textTheme.bodyMedium,
          ),
          Row(
            children: [
              SvgPicture.asset(AppIcons.riyal),
              const SizedBox(width: 2),
              Text(
                value.toString(),
                style: isTotal
                    ? context.textTheme.bodyMedium?.w700
                    : context.textTheme.labelLarge,
              ),
            ],
          ),
        ],
      ),
    );
  }
}

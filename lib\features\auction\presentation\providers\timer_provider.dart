import 'dart:async';
import 'package:bond_core/bond_core.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/core/utils/timezone_utils.dart';
import 'package:zod/features/auction/data/api.dart';

final countdownTimerProvider = StateNotifierProvider.family<
    CountdownTimerNotifier, CountdownTimerState, DateTime>(
  (ref, targetTime) => CountdownTimerNotifier(targetTime, null),
);

// Define a record type for the auction timer parameters
typedef AuctionTimerParams = ({DateTime endTime, int auctionId});

final auctionCountdownTimerProvider = StateNotifierProvider.family<
    CountdownTimerNotifier, CountdownTimerState, AuctionTimerParams>(
  (ref, params) => CountdownTimerNotifier(params.endTime, params.auctionId),
);

class CountdownTimerState {
  final Duration remainingTime;
  final bool isVisible;
  final bool hasTriggeredEndEvent;

  CountdownTimerState({
    required this.remainingTime,
    required this.isVisible,
    this.hasTriggeredEndEvent = false,
  });

  CountdownTimerState copyWith({
    Duration? remainingTime,
    bool? isVisible,
    bool? hasTriggeredEndEvent,
  }) {
    return CountdownTimerState(
      remainingTime: remainingTime ?? this.remainingTime,
      isVisible: isVisible ?? this.isVisible,
      hasTriggeredEndEvent: hasTriggeredEndEvent ?? this.hasTriggeredEndEvent,
    );
  }

  bool get isInRemaining =>
      remainingTime.isNegative || remainingTime.inSeconds == 0;

  /// Returns true if the timer has ended (remaining time is zero or negative)
  bool get isTimerEnded =>
      remainingTime.isNegative ||
      remainingTime.inSeconds == 0 ||
      hasTriggeredEndEvent;

  String get formatDuration {
    if (remainingTime.isNegative) return "00:00:00";
    final hours = remainingTime.inHours.toString().padLeft(2, '0');
    final minutes = (remainingTime.inMinutes % 60).toString().padLeft(2, '0');
    final seconds = (remainingTime.inSeconds % 60).toString().padLeft(2, '0');
    if (hours == "00") return "00:$minutes:$seconds";
    return " $hours:$minutes:$seconds";
  }
}

class CountdownTimerNotifier extends StateNotifier<CountdownTimerState> {
  late Timer _timer;
  final DateTime _targetTime;
  final int? _auctionId;

  CountdownTimerNotifier(this._targetTime, this._auctionId)
      : super(CountdownTimerState(
          remainingTime: const Duration(),
          isVisible: true, // Always set to true by default
          hasTriggeredEndEvent: false,
        )) {
    _startTimer();
  }

  void _startTimer() {
    final now = TimezoneUtils.getCurrentLocalTime();

    print('TIMER_PROVIDER - Target time: $_targetTime');
    print('TIMER_PROVIDER - Current time: $now');
    print(
        'TIMER_PROVIDER - Is target time before now: ${_targetTime.isBefore(now)}');

    // Check if the target time is in the past
    if (_targetTime.isBefore(now)) {
      print(
          'TIMER_PROVIDER - Target time is in the past, setting isVisible to false');
      state = state.copyWith(
        remainingTime: const Duration(),
        isVisible: false,
        hasTriggeredEndEvent: true,
      );

      // If we have an auction ID and haven't triggered the end event yet, trigger it now
      if (_auctionId != null) {
        _triggerAuctionEnded(_auctionId);
      }

      return;
    }

    final remaining = _targetTime.difference(now);
    // Keep the timer visible when there are minutes or seconds remaining
    // Only hide it when the timer has completely ended
    final isVisible = remaining.inSeconds > 0;

    print('TIMER_PROVIDER - Remaining seconds: ${remaining.inSeconds}');
    print('TIMER_PROVIDER - isVisible: $isVisible');

    // Check if the timer has already ended
    final hasEnded = remaining.isNegative || remaining.inSeconds == 0;
    print('TIMER_PROVIDER - hasEnded: $hasEnded');

    state = CountdownTimerState(
      remainingTime: remaining,
      isVisible: isVisible,
      // If the timer has already ended when initializing, mark it as having triggered the end event
      // This prevents triggering the event when the widget rebuilds after the auction has ended
      hasTriggeredEndEvent: hasEnded,
    );

    if (isVisible) {
      _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
        final now = TimezoneUtils.getCurrentLocalTime();
        final remaining = _targetTime.difference(now);

        if (remaining.isNegative || remaining.inSeconds == 0) {
          _timer.cancel();

          // If we have an auction ID and haven't triggered the end event yet, trigger it now
          if (_auctionId != null && !state.hasTriggeredEndEvent) {
            _triggerAuctionEnded(_auctionId);

            // Update state to mark that we've triggered the end event
            state = state.copyWith(
                remainingTime: const Duration(),
                isVisible: false,
                hasTriggeredEndEvent: true);
          } else {
            // Just update the timer state without triggering the event again
            state = state.copyWith(
                remainingTime: const Duration(), isVisible: false);
          }
        } else {
          state = state.copyWith(remainingTime: remaining);
        }
      });
    } else if (hasEnded && _auctionId != null && !state.hasTriggeredEndEvent) {
      // If the timer has already ended when initializing and we haven't triggered the event yet, trigger it now
      _triggerAuctionEnded(_auctionId);

      // Update state to mark that we've triggered the end event
      state = state.copyWith(
          remainingTime: const Duration(),
          isVisible: false,
          hasTriggeredEndEvent: true);
    }
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  /// Trigger the auction ended event when the timer finishes
  Future<void> _triggerAuctionEnded(int auctionId) async {
    try {
      final api = sl<AuctionApi>();
      await api.triggerAuctionEnded(auctionId);
    } catch (e) {
      // Error handled silently
    }
  }

  String formatDuration(Duration duration) {
    if (duration.isNegative) return "00:00:00";
    final hours = duration.inHours.toString().padLeft(2, '0');
    final minutes = (duration.inMinutes % 60).toString().padLeft(2, '0');
    final seconds = (duration.inSeconds % 60).toString().padLeft(2, '0');
    return "$hours:$minutes:$seconds";
  }
}

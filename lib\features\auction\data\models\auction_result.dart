import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:bond_core/bond_core.dart';
import 'package:zod/features/auction/data/models/auction_result_statistics.dart';
import 'package:zod/features/auction/data/models/auction_winner.dart';
import 'package:zod/features/auction/data/models/auction_user_activity.dart';

part 'auction_result.g.dart';

@JsonSerializable(explicitToJson: true)
class AuctionResult extends Equatable with Jsonable {
  final AuctionResultStatistics statistics;
  final AuctionWinner winner;
  
  @JsonKey(name: 'has_prize_claim', defaultValue: false)
  final bool hasPrizeClaim ;
  
  @JsonKey(name: 'user_activity')
  final AuctionUserActivity userActivity;
  
  final String message;

  const AuctionResult({
    required this.statistics,
    required this.winner,
    required this.userActivity,
    required this.message,
    required this.hasPrizeClaim,
  });

  factory AuctionResult.fromJson(Map<String, dynamic> json) =>
      _$AuctionResultFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$AuctionResultToJson(this);

  @override
  List<Object?> get props => [statistics, winner, userActivity, message];
}

import 'package:flutter/material.dart';

class AppColors {
  AppColors._();

  static const primaryColor = Color.fromARGB(255, 73, 85, 205);
  static const secondaryColor = Color.fromARGB(255, 17, 24, 39);

  static const textColor = Color.fromARGB(255, 17, 24, 39);

  static const borderColor = Color.fromARGB(255, 99, 106, 249);
  static const royalBlue = Color.fromARGB(255, 68, 76, 231);
  static const periwinkle = Color.fromARGB(255, 208, 211, 255);
  static const disabled = Color.fromARGB(255, 249, 250, 251);
  static const lightSlateGray = Color.fromARGB(255, 156, 163, 175);
  static const slateGray = Color.fromARGB(255, 107, 114, 128);
  static const athensGray = Color.fromARGB(255, 237, 238, 241);
  static const lavenderMist = Color.fromARGB(255, 239, 240, 255);
  static const softWhite = Color.fromARGB(255, 249, 250, 251);
  static const snowWhite = Color.fromRGBO(255, 250, 250, 1);

  static const white = Colors.white;
  static const red = Colors.red;
  static const crimsonRed = Color.fromARGB(255, 220, 38, 38);
  static const lightRed = Color.fromARGB(255, 245, 242, 242);
  static const darkRed = Color.fromARGB(255, 185, 28, 28);
  static const tomatoRed = Color.fromARGB(255, 239, 68, 68);
  static const coralPink = Color.fromARGB(255, 248, 113, 113);
  static const lightGoldenrod = Color.fromARGB(255, 254, 240, 138);
  static const goldenrod = Color.fromARGB(255, 234, 179, 8);
  static const russetBrown = Color.fromARGB(255, 133, 77, 14);
  static const goldenBrown = Color.fromARGB(255, 161, 98, 7);
  static const ivory = Color.fromARGB(255, 254, 252, 232);

  static const gunmetalGrayLight = Color.fromARGB(13, 16, 24, 40);
  static const softPeriwinkle = Color.fromARGB(255, 154, 159, 255);
  static const platinum = Color.fromARGB(255, 237, 237, 237);
  static const lightPlatinum = Color.fromARGB(255, 237, 238, 241);
  static const light = Color.fromARGB(255, 245, 247, 248);
  static const lightGray = Color.fromARGB(255, 229, 231, 235);
  static const lighterGray = Color.fromARGB(255, 220, 225, 232);
  static const softSilver = Color.fromARGB(255, 243, 244, 246);
  static const softSteel = Color.fromARGB(255, 209, 213, 219);
  static const softSteel1 = Color.fromARGB(255, 226, 229, 232);
  static const steelGray = Color.fromARGB(255, 75, 85, 99);
  static const darkSteel = Color.fromARGB(255, 55, 65, 81);
  static const charcoalGray = Color.fromRGBO(31, 41, 55, 1);
  static const blackShadow = Color.fromARGB(13, 0, 0, 0);
  static const black = Colors.black;
  static const green = Color.fromARGB(255, 74, 222, 128);
  static const lightGreen = Color.fromARGB(255, 240, 253, 244);
  static const mintCream = Color.fromARGB(255, 236, 253, 243);
  static const darkGreen = Color.fromARGB(255, 22, 101, 52);
  static const activeGreen = Color.fromARGB(255, 60, 173, 103);
  static const deepGreen = Color.fromARGB(255, 6, 118, 71);
  static const mintGreen = Color.fromARGB(255, 171, 239, 198);
  static const blue = Colors.blue;
  static const lightBlue = Colors.lightBlue;
  static const darkBlue = Colors.blueGrey;
  static const iceBlue = Color.fromARGB(255, 238, 244, 255);
  static const softSkyBlue = Color.fromARGB(255, 199, 215, 254);
  static const deepBlue = Color.fromARGB(255, 53, 53, 205);
  static const progressBarColor = Color.fromARGB(255, 35, 164, 245);
  static const wornBackground = Color(0xffFEFCE8);
  static const mybidBackground = Color(0xffEFF0FF); //
  static const redBackground = Color(0xffFEE2E2); //
  static const redBackground2 = Color(0xffFFB0B1); //
  static const winnerYellow = Color(0xFFFACC15); // rgba(250, 204, 21, 1)
  static const redButton = Color(0xffFF4545); //
  static const backgroudUnReadNotification = Color(0xffFBFBFF);
}

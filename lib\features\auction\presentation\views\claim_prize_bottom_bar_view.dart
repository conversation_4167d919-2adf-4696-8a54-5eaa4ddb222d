import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/app/routes.dart';
import 'package:zod/features/auction/data/models/claim_prize.dart';
import 'package:zod/features/auction/presentation/providers/timer_provider.dart';
import 'package:zod/features/auction/presentation/views/extra_time_request_view.dart';
import 'package:zod/features/auction/presentation/views/payment_button_view.dart';
import 'package:zod/features/home/<USER>/models/address.dart';

class ClaimPrizeBottomBarView extends ConsumerWidget {
  final int auctionId;
  final ClaimPrize claimPrize;
  final Address? selectedAddress;
  final CountdownTimerState timerState;

  const ClaimPrizeBottomBarView({
    super.key,
    required this.auctionId,
    required this.claimPrize,
    required this.timerState,
    this.selectedAddress,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Payment button
          PaymentButtonView(
            auctionId: auctionId,
            claimPrize: claimPrize,
            selectedAddress: selectedAddress,
            timerState: timerState,
          ),
          const SizedBox(height: 8),
          // Extra time request link
          GestureDetector(
            onTap: () => goRouter.push('/extra-time/$auctionId'),
            child: ExtraTimeRequestView(
              auctionId: auctionId,
              claimPrize: claimPrize,
              timerState: timerState,
            ),
          ),
        ],
      ),
    );
  }
}

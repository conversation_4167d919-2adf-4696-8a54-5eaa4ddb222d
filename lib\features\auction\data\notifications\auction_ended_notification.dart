import 'dart:convert';

import 'package:bond_notifications/bond_notifications.dart';
import 'package:zod/app/routes.dart';
import 'package:zod/core/notifications/notification_alert_view.dart';

class AuctionEndedPushNotification extends PushNotification
    with ActionablePushNotification {
  AuctionEndedPushNotification();

  @override
  List<String> get code => ['auction_ended'];

  @override
  void onNotification(NotificationData data) {}

  @override
  void onNotificationTapped(NotificationData data) {
          if (data.containsKey("auction_id")) {
        final auctionId = data["auction_id"];
        goRouter.push('/auction/$auctionId');
        return;
      }
    // The data['data'] is a JSON string, so we need to parse it
    final dataString = data['data'] as String;

    // Parse the JSON string to get the auction_id
    final dataMap = jsonDecode(dataString);
    final auctionId = dataMap['auction_id'];

    // Navigate to the auction details page
    goRouter.push('/auction/$auctionId');
  }
}

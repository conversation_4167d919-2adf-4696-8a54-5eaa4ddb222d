import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:simple_shadow/simple_shadow.dart';
import 'package:zod/app/routes.dart';
import 'package:zod/core/resources/app_icons.dart';
import 'dart:math' as math;

class ArrowAppBar extends ConsumerWidget implements PreferredSizeWidget {
  const ArrowAppBar({
    super.key,
    this.extendBodyBehindAppBar = false,
    this.backgroundColor = Colors.white,
    this.hideBack = false,
  });

  final bool extendBodyBehindAppBar;
  final bool hideBack;
  final Color backgroundColor;

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    bool isRTL = Directionality.of(context) == TextDirection.rtl;
    return AppBar(
      automaticallyImplyLeading: false,
      centerTitle: false,
      systemOverlayStyle: SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        statusBarBrightness: Brightness.light,
      ),
      title: Transform.translate(
        offset: Offset(isRTL ? 20 : -20, 0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Visibility(
              visible: !hideBack,
              child: InkWell(
                highlightColor: Colors.transparent,
                splashColor: Colors.transparent,
                onTap: () => goRouter.pop(true),
                child: SimpleShadow(
                    color: Colors.black,
                    opacity: 0.06,
                    offset: Offset(0, 6),
                    sigma: 10,
                    child: Transform.rotate(
                        angle: isRTL ? math.pi : 0,
                        child: SvgPicture.asset(AppIcons.backArrow))),
              ),
            ),
          ],
        ),
      ),
      elevation: 0,
      leadingWidth: 0,
      backgroundColor: backgroundColor,
    );
  }
}

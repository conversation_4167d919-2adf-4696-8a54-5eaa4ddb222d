import 'dart:async';
import 'dart:developer';

import 'package:zod/core/services/websocket_service.dart';
import 'package:zod/features/auction/data/api.dart';

class AuctionWebSocketService {
  final WebSocketService _webSocketService;
  //is connected 
  bool get isConnected => _webSocketService.isConnected;

  // Private constructor for dependency injection in tests
  AuctionWebSocketService._(this._webSocketService);

  // Singleton pattern
  static final AuctionWebSocketService _instance =
      AuctionWebSocketService._(WebSocketService());

  // Factory constructor to return the singleton instance
  factory AuctionWebSocketService() => _instance;

  Future<void> initialize() async {
    await _webSocketService.initialize();
  }

  Future<void> subscribeToAuction(int auctionId) async {
    final channelName = 'auction.$auctionId';
    await _webSocketService.subscribeToChannel(channelName);
    log('Pusher: Subscribed to auction channel: $channelName');
  }

  Future<void> unsubscribeFromAuction(int auctionId) async {
    final channelName = 'auction.$auctionId';
    await _webSocketService.unsubscribeFromChannel(channelName);
    log('Pusher: Unsubscribed from auction channel: $channelName');
  }

  Stream<AuctionApiJoin> listenToBids(int auctionId) {
    final channelName = 'auction.$auctionId';
    final eventName = 'bid.placed';

    return _webSocketService.listenToEvent(channelName, eventName).map((data) {
      log('Pusher: Received bid event:  EEEE  ');
      return AuctionApiJoin.fromJson(data);
    });
  }
  Stream<AuctionApiJoin> listenToUserJoined(int auctionId) {
    final channelName = 'auction.$auctionId';
    final eventName = 'user.joined';

    return _webSocketService.listenToEvent(channelName, eventName).map((data) {
      log('Pusher: Received bid event:  EEEE  ');
      return AuctionApiJoin.fromJson(data);
    });
  }

  Stream<AuctionApiJoin> listenToAuctionStarted(int auctionId) {
    final channelName = 'auction.$auctionId';
    final eventName = 'auction.started';

    return _webSocketService.listenToEvent(channelName, eventName).map((data) {
      log('Pusher:  2Received auction started event: $data');
      return AuctionApiJoin.fromJson(data);
    });
  }

  Stream<AuctionApiJoin> listenToAuctionEnded(int auctionId) {
    final channelName = 'auction.$auctionId';
    final eventName = 'auction.ended';

    return _webSocketService.listenToEvent(channelName, eventName).map((data) {
      log('PUSHER LISTENER - Received auction ended event for auction $auctionId');
      log('PUSHER LISTENER - Auction ended event data: $data');
      return AuctionApiJoin.fromJson(data);
    });
  }

  Stream<AuctionApiJoin> listenToLastHourTimeAuction(int auctionId) {
    final channelName = 'auction.$auctionId';
    final eventName = 'auction.last_hour';

    return _webSocketService.listenToEvent(channelName, eventName).map((data) {
      log('PUSHER LISTENER - Received auction last hour event for auction $auctionId');
      log('PUSHER LISTENER - Auction last hour event data: $data');
      return AuctionApiJoin.fromJson(data);
    });
  }

  void dispose() {
    _webSocketService.dispose();
  }
}

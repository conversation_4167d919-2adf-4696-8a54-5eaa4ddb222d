import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class ShimmerAuctionDetailsView extends StatelessWidget {
  const ShimmerAuctionDetailsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 12),
          _shimmerContainer(height: 20, width: 150), // Auction status
          SizedBox(height: 8),
          _shimmerContainer(height: 24, width: double.infinity), // Title
          SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _shimmerContainer(height: 20, width: 100), // Price
              _shimmerContainer(height: 20, width: 80),  // Coins View
            ],
          ),
          SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _shimmerContainer({required double height, required double width}) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        height: height,
        width: width,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
}

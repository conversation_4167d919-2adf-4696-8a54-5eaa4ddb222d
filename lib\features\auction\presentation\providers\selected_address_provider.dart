import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/features/auction/presentation/providers/user_addresses_provider.dart';
import 'package:zod/features/home/<USER>/models/address.dart';

final selectedAddressProvider =
    StateNotifierProvider<SelectedAddressNotifier, Address?>((ref) {
  // Create the notifier with null initial state
  final notifier = SelectedAddressNotifier(null);

  // Set up a one-time listener for the initial data
  ref.listenSelf((previous, next) {
    if (previous == null && next == null) {
      // Only check once when the provider is first initialized
      final addressesAsync = ref.read(userAddressesProvider);
      addressesAsync.whenData((addresses) {
        if (addresses.isNotEmpty) {
          // Set the first address as selected
          notifier.selectAddress(addresses.first);
        }
      });
    }
  });

  return notifier;
});

class SelectedAddressNotifier extends StateNotifier<Address?> {
  SelectedAddressNotifier(super.initialAddress);

  void selectAddress(Address address) {
    state = address;
  }
}

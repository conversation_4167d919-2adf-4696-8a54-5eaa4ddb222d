import 'package:flutter/material.dart';
import 'package:zod/core/resources/app_colors.dart';

class RadioButtonView extends StatelessWidget {
  final bool isSelected;
  
  const RadioButtonView({
    super.key,
    required this.isSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 20,
      height: 20,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: isSelected ? AppColors.royalBlue : AppColors.lightGray,
          width: 2,
        ),
      ),
      child: isSelected
          ? Center(
              child: Container(
                width: 10,
                height: 10,
                decoration: const BoxDecoration(
                  shape: BoxShape.circle,
                  color: AppColors.royalBlue,
                ),
              ),
            )
          : null,
    );
  }
}

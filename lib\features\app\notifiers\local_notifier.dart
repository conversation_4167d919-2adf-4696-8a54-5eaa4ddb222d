import 'dart:developer';
import 'dart:ui';

import 'package:bond_cache/bond_cache.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/core/analytics/events/events.dart';
import 'package:zod/core/app_analytics.dart';

class LocalNotifier extends Notifier<Locale> {
  @override
  Locale build() => Locale('en');

  void update(Locale locale) {
    Cache.put('language', locale.languageCode);
    state = locale;

    // Track app language event
    AppAnalytics.fire(AppLanguageEvent(language: locale.languageCode));
    log('App language event fired: ${locale.languageCode}',
        name: 'LocalNotifier');
  }
}

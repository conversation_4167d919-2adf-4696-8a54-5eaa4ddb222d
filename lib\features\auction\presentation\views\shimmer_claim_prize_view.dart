import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import 'package:zod/core/resources/app_colors.dart';

class ShimmerClaimPrizeView extends StatelessWidget {
  const ShimmerClaimPrizeView({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Product Card Shimmer
          ShimmerProductCardView(),
          const SizedBox(height: 24),
          // Delivery Address Section Shimmer
          ShimmerDeliveryAddressSectionView(),
          const SizedBox(height: 16),
          // Order Summary Section Shimmer
          ShimmerOrderSummarySectionView(),
          const SizedBox(height: 120),
        ],
      ),
    );
  }
}

class ShimmerProductCardView extends StatelessWidget {
  const ShimmerProductCardView({super.key});

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: AppColors.platinum.withOpacity(0.4),
      highlightColor: AppColors.platinum.withOpacity(0.2),
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: AppColors.athensGray),
        ),
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Row(
                children: [
                  // Product image
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: AppColors.white,
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  const SizedBox(width: 16),
                  // Product details
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          width: double.infinity,
                          height: 16,
                          decoration: BoxDecoration(
                            color: AppColors.white,
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            // Timer bar
            Container(
              width: double.infinity,
              height: 40,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(16),
                  bottomRight: Radius.circular(16),
                ),
                color: AppColors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class ShimmerDeliveryAddressSectionView extends StatelessWidget {
  const ShimmerDeliveryAddressSectionView({super.key});

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: AppColors.platinum.withOpacity(0.4),
      highlightColor: AppColors.platinum.withOpacity(0.2),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Container(
            width: 150,
            height: 18,
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          const SizedBox(height: 8),
          // Description
          Container(
            width: double.infinity,
            height: 14,
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          const SizedBox(height: 12),
          // Address container or add address button
          Container(
            width: double.infinity,
            height: 100,
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppColors.softSteel),
            ),
          ),
        ],
      ),
    );
  }
}

class ShimmerOrderSummarySectionView extends StatelessWidget {
  const ShimmerOrderSummarySectionView({super.key});

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: AppColors.platinum.withOpacity(0.4),
      highlightColor: AppColors.platinum.withOpacity(0.2),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Container(
            width: 120,
            height: 18,
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          const SizedBox(height: 16),
          // Order summary container
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: AppColors.softSilver),
            ),
            child: Column(
              children: [
                const SizedBox(height: 12),
                // Product Price
                _buildShimmerRow(),
                const SizedBox(height: 12),
                // Auction Bids
                _buildShimmerRow(),
                const SizedBox(height: 12),
                // Summary section
                Container(
                  padding: EdgeInsets.zero,
                  decoration: BoxDecoration(
                    color: AppColors.softWhite,
                  ),
                  child: Column(
                    children: [
                      const SizedBox(height: 5),
                      // Remaining to Pay
                      _buildShimmerRow(),
                      const SizedBox(height: 12),
                      // Tax
                      _buildShimmerRow(),
                      const SizedBox(height: 12),
                      // Total To Pay
                      _buildShimmerRow(),
                      const SizedBox(height: 12),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShimmerRow() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            width: 120,
            height: 16,
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          Container(
            width: 80,
            height: 16,
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ],
      ),
    );
  }
}

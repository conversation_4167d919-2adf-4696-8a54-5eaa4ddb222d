import 'dart:async';
import 'dart:developer' as dev;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/core/utils/timezone_utils.dart';
import 'package:zod/features/auction/data/models/auction.dart';

/// Provider that monitors if an auction is in its last hour
final auctionLastHourProvider =
    StateNotifierProvider.family<AuctionLastHourNotifier, bool, Auction>(
  (ref, auction) => AuctionLastHourNotifier(auction),
);

class AuctionLastHourNotifier extends StateNotifier<bool> {
  Timer? _timer;
  final Auction _auction;

  AuctionLastHourNotifier(this._auction) : super(false) {
    // Check if the auction is already in its last hour
    final isInLastHour = _auction.isInLastHour;
    dev.log('AUCTION_LAST_HOUR_NOTIFIER - Initial isInLastHour: $isInLastHour');
    dev.log('AUCTION_LAST_HOUR_NOTIFIER - Auction ID: ${_auction.id}');
    dev.log(
        'AUCTION_LAST_HOUR_NOTIFIER - Auction end time: ${_auction.endedAt}');

    // IMPORTANT: Always initialize state to true if the auction is in its last hour
    // This ensures the hurry up notification is shown
    if (isInLastHour) {
      dev.log(
          'AUCTION_LAST_HOUR_NOTIFIER - Setting state to true because auction is in last hour');
      state = true;
    }

    // Start periodic checks
    _checkLastHourStatus();
    _startTimer();
  }

  void _checkLastHourStatus() {
    // Simple check: if the auction has no end time, it's not in the last hour
    if (_auction.endedAt == null) {
      dev.log('AUCTION_LAST_HOUR - Auction has no end time');
      state = false;
      return;
    }

    // We're removing the status check entirely because the hurry up notification
    // should be shown based on time remaining, not the auction status

    // Get the current time in the app's timezone (Riyadh)
    final now = TimezoneUtils.getCurrentLocalTime();

    // Calculate the difference in seconds between the end time and now using TimezoneUtils
    // This ensures both dates are in the same timezone before calculating the difference
    final differenceInSeconds =
        TimezoneUtils.getSecondsDifference(_auction.endedAt!, now);

    // Calculate hours, minutes, and seconds
    final hours = differenceInSeconds ~/ 3600;
    final minutes = (differenceInSeconds % 3600) ~/ 60;
    final seconds = differenceInSeconds % 60;

    // Log the time values for debugging
    dev.log('AUCTION_LAST_HOUR - Auction ID: ${_auction.id}');
    dev.log('AUCTION_LAST_HOUR - Difference in seconds: $differenceInSeconds');
    dev.log(
        'AUCTION_LAST_HOUR - Hours: $hours, Minutes: $minutes, Seconds: $seconds');
    dev.log('AUCTION_LAST_HOUR - End time: ${_auction.endedAt}');
    dev.log('AUCTION_LAST_HOUR - Current time: $now');

    // The auction is in its last hour when there are 60 minutes or less remaining
    // but more than 0 seconds (not yet ended)
    // We want to show it when the timer is at 1:00:00 or less
    // Using 3605 seconds (1 hour + 5 seconds) as the threshold to catch edge cases
    final isLastHour = differenceInSeconds <= 3605 && differenceInSeconds > 0;

    dev.log('AUCTION_LAST_HOUR - isLastHour: $isLastHour');

    // IMPORTANT: If the auction is in its last hour, always set state to true
    // This ensures the hurry up notification is shown
    if (isLastHour) {
      dev.log(
          'AUCTION_LAST_HOUR - Setting state to true because auction is in last hour');
      state = true;
      return;
    }

    state = isLastHour;
  }

  void _startTimer() {
    // Cancel any existing timer
    _timer?.cancel();

    // Check every 10 seconds if the auction has entered its last hour
    _timer = Timer.periodic(const Duration(seconds: 10), (_) {
      _checkLastHourStatus();
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }
}

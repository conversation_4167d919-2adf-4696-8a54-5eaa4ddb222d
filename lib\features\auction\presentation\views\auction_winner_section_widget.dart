import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/app_theme.dart';
import 'package:zod/core/resources/app_assets.dart';
import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/core/resources/app_icons.dart';
import 'package:zod/core/widgets/network_image_widget.dart';
import 'package:zod/features/auction/data/models/auction.dart';
import 'package:zod/features/auction/data/models/auction_meta.dart';
import 'package:zod/features/auction/presentation/views/auction_avatar_row_widget.dart';

class AuctionWinnerSectionWidget extends StatelessWidget {
  final Auction auction;
  final AuctionMeta? auctionMeta;

  const AuctionWinnerSectionWidget({
    super.key,
    required this.auction,
    this.auctionMeta,
  });

  // Get the winner information from the auction meta result
  bool get hasWinnerInfo => auctionMeta?.result != null;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 270,
      width: double.infinity,
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.lightGoldenrod),
        borderRadius: BorderRadius.circular(16),
      ),
      // Set a minimum height to ensure the background displays properly
      constraints: const BoxConstraints(minHeight: 240),
      child: Stack(
        alignment: Alignment.center,
        clipBehavior: Clip.none,
        children: [
          // Background SVG
          Positioned.fill(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Image.asset(
                AppImagesAssets.auctionCompetition,
                fit: BoxFit.cover,
              ),
            ),
          ),

          Positioned(
            top: 16,
            child: Text(context.localizations.auction_winner,
                style: context.textTheme.labelSmall?.w700),
          ),
          Positioned(
            top: 46,
            child: Stack(
              children: [
                Container(
                    width: 70,
                    height: 70,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: AppColors.winnerYellow,
                    ),
                    clipBehavior: Clip.antiAlias,
                    child: FittedBox(
                      fit: BoxFit.cover,
                      child: NetworkImageView(
                        auctionMeta?.result?.winner.avatar ?? '',
                      ),
                    )),
                PositionedDirectional(
                  top: 0,
                  end: 0,
                  child: SvgPicture.asset(AppIcons.kingFill),
                ),
              ],
            ),
          ),
          Positioned(
            top: 111,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(height: 16),
                Text(
                  auctionMeta?.result?.winner.name ?? '',
                  style: context.textTheme.labelLarge?.w700,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 4),
                Text(
                  context.localizations.placed_bids_in_auction(
                      auctionMeta?.result?.statistics.winnerBids ?? 0),
                  style: context.textTheme.labelMedium,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                AuctionAvatarRowWidget(auction: auction),
                const SizedBox(height: 6),
                Text(
                  context.localizations.bidders_total_bids(
                    auctionMeta?.result?.statistics.totalBidders ?? 0,
                    auctionMeta?.result?.statistics.totalBids ?? 0,
                  ),
                  style: context.textTheme.labelMedium?.slateGray,
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

import 'package:bond_cache/bond_cache.dart';

import '../core/cache/secure_storage_cache_driver.dart';

class CacheConfig {
  static var defaultStore = 'shared_preference';

  static var stores = {
    'shared_preference': {
      'driver': 'shared_preference',
      'class': SharedPreferencesCacheDriver,
    },
    'in_memory': {
      'driver': 'in_memory',
      'class': InMemoryCacheDriver,
    },
    'secure_cache': {
      'driver': 'secure_cache',
      'class': SecureStorageCacheDriver,
    },
  };
}

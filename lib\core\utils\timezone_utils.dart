// ignore: depend_on_referenced_packages
import 'package:timezone/timezone.dart' as tz;

/// Utility class for timezone operations
class TimezoneUtils {
  /// The default timezone for the app (Riyadh, Saudi Arabia)
  static const String defaultTimezone = 'Asia/Riyadh';

  /// Convert a UTC DateTime to the app's default timezone (Riyadh)
  static DateTime toLocalTime(DateTime utcTime) {
    final location = tz.getLocation(defaultTimezone);
    final tzDateTime = tz.TZDateTime.from(utcTime, location);
    return DateTime(
      tzDateTime.year,
      tzDateTime.month,
      tzDateTime.day,
      tzDateTime.hour,
      tzDateTime.minute,
      tzDateTime.second,
      tzDateTime.millisecond,
      tzDateTime.microsecond,
    );
  }

  /// Convert a local DateTime to UTC
  static DateTime toUtcTime(DateTime localTime) {
    return localTime.toUtc();
  }

  /// Get the current time in the app's default timezone (Riyadh)
  static DateTime getCurrentLocalTime() {
    final location = tz.getLocation(defaultTimezone);
    final now = tz.TZDateTime.now(location);
    return DateTime(
      now.year,
      now.month,
      now.day,
      now.hour,
      now.minute,
      now.second,
      now.millisecond,
      now.microsecond,
    );
  }

  /// Format a DateTime to a string in the app's default timezone
  static String formatDateTime(DateTime dateTime,
      {String format = 'yyyy-MM-dd HH:mm:ss'}) {
    // Convert to local time first
    final localTime = toLocalTime(dateTime.toUtc());

    // You can use intl package's DateFormat here for more complex formatting
    return localTime.toString();
  }

  /// Calculate the difference in seconds between two DateTime objects
  /// Both dates will be converted to the app's default timezone before calculating the difference
  static int getSecondsDifference(DateTime date1, DateTime date2) {
    // Convert both dates to the app's default timezone
    final location = tz.getLocation(defaultTimezone);
    final tzDate1 = tz.TZDateTime.from(date1, location);
    final tzDate2 = tz.TZDateTime.from(date2, location);

    // Calculate the difference in seconds
    final diffInSeconds = tzDate1.difference(tzDate2).inSeconds;
    return diffInSeconds;
  }
}

import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import 'package:zod/core/resources/app_colors.dart';

class ShimmerAddAddressView extends StatelessWidget {
  const ShimmerAddAddressView({super.key});

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) => Column(
        children: [
          // Map shimmer
          SizedBox(
            height: constraints.maxHeight * 0.45,
            child: const ShimmerMapView(),
          ),
          // Form shimmer
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.only(bottom: 16),
              child: const ShimmerAddressFormView(),
            ),
          ),
        ],
      ),
    );
  }
}

class ShimmerMapView extends StatelessWidget {
  const ShimmerMapView({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(bottom: 8),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Shimmer.fromColors(
          baseColor: AppColors.platinum.withOpacity(0.4),
          highlightColor: AppColors.platinum.withOpacity(0.2),
          child: Container(
            color: AppColors.white,
          ),
        ),
      ),
    );
  }
}

class ShimmerAddressFormView extends StatelessWidget {
  const ShimmerAddressFormView({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Center handle
          Center(
            child: Shimmer.fromColors(
              baseColor: AppColors.platinum.withOpacity(0.4),
              highlightColor: AppColors.platinum.withOpacity(0.2),
              child: Container(
                width: 30,
                height: 3,
                decoration: BoxDecoration(
                  color: AppColors.white,
                  borderRadius: BorderRadius.circular(100),
                ),
              ),
            ),
          ),
          const SizedBox(height: 10),
          // Title shimmer
          Shimmer.fromColors(
            baseColor: AppColors.platinum.withOpacity(0.4),
            highlightColor: AppColors.platinum.withOpacity(0.2),
            child: Container(
              width: 120,
              height: 20,
              decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),
          const SizedBox(height: 12),
          // Building Name field shimmer
          _buildFormFieldShimmer(context, 'Building Name'),
          const SizedBox(height: 16),
          // Street field shimmer
          _buildFormFieldShimmer(context, 'Street'),
          const SizedBox(height: 16),
          // Phone Number field shimmer
          _buildFormFieldShimmer(context, 'Phone Number'),
        ],
      ),
    );
  }

  Widget _buildFormFieldShimmer(BuildContext context, String label) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label shimmer
        Shimmer.fromColors(
          baseColor: AppColors.platinum.withOpacity(0.4),
          highlightColor: AppColors.platinum.withOpacity(0.2),
          child: Container(
            width: 100,
            height: 14,
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ),
        const SizedBox(height: 8),
        // Text field shimmer
        Shimmer.fromColors(
          baseColor: AppColors.platinum.withOpacity(0.4),
          highlightColor: AppColors.platinum.withOpacity(0.2),
          child: Container(
            width: double.infinity,
            height: 48,
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppColors.softSteel),
            ),
          ),
        ),
      ],
    );
  }
}

class ShimmerSaveAddressButtonView extends StatelessWidget {
  const ShimmerSaveAddressButtonView({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: Shimmer.fromColors(
        baseColor: AppColors.platinum.withOpacity(0.4),
        highlightColor: AppColors.platinum.withOpacity(0.2),
        child: Container(
          width: double.infinity,
          height: 48,
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
    );
  }
}

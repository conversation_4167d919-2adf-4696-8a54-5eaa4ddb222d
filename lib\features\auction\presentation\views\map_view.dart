import 'dart:io';

import 'package:custom_info_window/custom_info_window.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:zod/core/resources/app_icons.dart';
import 'package:zod/features/auction/presentation/providers/add_address_controller_provider.dart';
import 'package:zod/features/auction/presentation/providers/map_location_provider.dart';

class MapView extends ConsumerStatefulWidget {
  const MapView({super.key});

  @override
  ConsumerState<MapView> createState() => _MapViewState();
}

class _MapViewState extends ConsumerState<MapView> {
  GoogleMapController? mapController;
  bool _isSimulator = false;

  @override
  void initState() {
    super.initState();
    _checkIfSimulator();
  }

  Future<void> _checkIfSimulator() async {
    if (Platform.isIOS) {
      final deviceInfo = DeviceInfoPlugin();
      final iosInfo = await deviceInfo.iosInfo;
      // Check if this is a simulator
      setState(() {
        _isSimulator = !iosInfo.isPhysicalDevice;
      });
    }
  }

  @override
  void dispose() {
    // Dispose of the map controller when the widget is disposed
    mapController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final mapState = ref.watch(mapLocationProvider);
    final addressController = ref.watch(addAddressControllerProvider.notifier);
    final addressState = ref.watch(addAddressControllerProvider);

    // If running on iOS simulator, show a placeholder instead of the map
    if (_isSimulator) {
      return _buildSimulatorPlaceholder(context, addressController);
    }

    // Set of gesture recognizers for the map
    final gestureRecognizers = <Factory<OneSequenceGestureRecognizer>>{};

    // Add all necessary gesture recognizers
    gestureRecognizers
        .add(Factory<PanGestureRecognizer>(() => PanGestureRecognizer()));
    gestureRecognizers
        .add(Factory<ScaleGestureRecognizer>(() => ScaleGestureRecognizer()));
    gestureRecognizers
        .add(Factory<TapGestureRecognizer>(() => TapGestureRecognizer()));
    gestureRecognizers.add(Factory<VerticalDragGestureRecognizer>(
        () => VerticalDragGestureRecognizer()));
    gestureRecognizers.add(Factory<HorizontalDragGestureRecognizer>(
        () => HorizontalDragGestureRecognizer()));

    return Stack(
      children: [
        // Google Map
        mapState.currentLocation != null
            ? GoogleMap(
                initialCameraPosition: CameraPosition(
                  target: mapState.currentLocation!,
                  zoom: 15,
                ),
                gestureRecognizers: gestureRecognizers,
                markers: Set<Marker>.from(mapState.markers.map((marker) {
                  // Create a new marker with onTap callback and ensure it's visible
                  return marker.copyWith(
                    onTapParam: () {
                      // When marker is tapped, get the address if we don't have it yet
                      // Otherwise, just show the info window
                      if (addressState.currentAddress == null) {
                        addressController.getAddressFromLatLng(marker.position);
                      } else {
                        // Show the info window with the current address
                        addressController.showCustomInfoWindow(marker.position);
                      }
                    },
                    visibleParam: true,
                    zIndexParam: 2,
                  );
                })),
                myLocationEnabled: true,
                myLocationButtonEnabled: false,
                zoomControlsEnabled: true,
                mapType: MapType.normal,
                // Enable all gesture controls for scrolling and zooming
                scrollGesturesEnabled: true,
                zoomGesturesEnabled: true,
                rotateGesturesEnabled: true,
                tiltGesturesEnabled: true,
                onTap: (LatLng position) async {
                  // Update marker position when map is tapped
                  await ref
                      .read(mapLocationProvider.notifier)
                      .updateMarkerPosition(position);

                  // Get address from the tapped location - this will also show the info window
                  await addressController.getAddressFromLatLng(position);

                  // Add a small delay to ensure the marker is visible
                  await Future.delayed(const Duration(milliseconds: 100));

                  // Explicitly show the info window again to ensure it's visible
                  addressController.showCustomInfoWindow(position);
                },
                onMapCreated: (GoogleMapController controller) {
                  // Store the controller locally
                  mapController = controller;

                  // Set the map controller in the address controller
                  addressController.setMapController(controller);

                  // Explicitly show the info window after a short delay
                  // This ensures the info window is displayed when the map is first loaded
                  if (mapState.currentLocation != null) {
                    // Use a longer delay to ensure the map is fully loaded
                    Future.delayed(const Duration(milliseconds: 1000),
                        () async {
                      // Make sure the marker is still visible
                      if (mapState.markers.isNotEmpty) {
                        // Force marker visibility update
                        await ref
                            .read(mapLocationProvider.notifier)
                            .updateMarkerPosition(mapState.currentLocation!);

                        // Add a small delay to ensure the marker is visible
                        await Future.delayed(const Duration(milliseconds: 100));

                        // Show the info window
                        addressController
                            .showCustomInfoWindow(mapState.currentLocation!);
                      }
                    });

                    // Force marker to stay visible after a longer delay
                    Future.delayed(const Duration(seconds: 3), () {
                      if (mapState.currentLocation != null) {
                        ref
                            .read(mapLocationProvider.notifier)
                            .updateMarkerPosition(mapState.currentLocation!);
                      }
                    });
                  }
                },
                onCameraMove: (position) {
                  // Update the custom info window position when camera moves
                  addressState.customInfoWindowController.onCameraMove!();
                },
              )
            : const Center(child: CircularProgressIndicator()),

        // Custom Info Window
        CustomInfoWindow(
          controller: addressState.customInfoWindowController,
          height: 60, // Increased height to accommodate content + triangle
          width: 250, // Wider for better visibility
          offset:
              5, // Small offset to position the info window above the marker
        ),

        // Only show loading indicator for initial map load, not for marker updates
        if (mapState.markers.isEmpty && !addressState.markersInitialized)
          const Center(
            child: CircularProgressIndicator(),
          ),

        // Error message (only show if there's no current location)
        if (mapState.error != null && mapState.currentLocation == null)
          Positioned(
            top: 10,
            left: 10,
            right: 10,
            child: Container(
              padding: const EdgeInsets.all(8),
              color: Colors.red.withOpacity(0.8),
              child: Text(
                mapState.error!,
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ),

        // Loading indicator - only show if markers are not initialized
        if (mapState.isLoading && !addressState.markersInitialized)
          const Center(child: CircularProgressIndicator()),

        // My location button
        Positioned(
          bottom: 88,
          right: 16,
          child: Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
            ),
            child: InkWell(
              onTap: () async {
                // Get current location
                await ref
                    .read(mapLocationProvider.notifier)
                    .getCurrentLocation();

                // Get address from the current location after location is updated
                if (mapState.currentLocation != null) {
                  // Get address and show info window
                  await addressController
                      .getAddressFromLatLng(mapState.currentLocation!);

                  // Add a small delay to ensure the marker is visible
                  await Future.delayed(const Duration(milliseconds: 100));

                  // Explicitly show the info window again to ensure it's visible
                  addressController
                      .showCustomInfoWindow(mapState.currentLocation!);
                }
              },
              child: SvgPicture.asset(AppIcons.gps),
            ),
          ),
        ),
      ],
    );
  }

  // Placeholder widget for iOS simulator
  Widget _buildSimulatorPlaceholder(
      BuildContext context, AddAddressController addressController) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Stack(
        children: [
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.map,
                  size: 64,
                  color: Colors.grey,
                ),
                const SizedBox(height: 16),
                const Text(
                  'Map not available in iOS simulator',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                const Text(
                  'Please use a physical device to test map functionality',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 14),
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    // Simulate a location selection with default coordinates (Riyadh, Saudi Arabia)
                    final defaultLocation = const LatLng(24.7136, 46.6753);
                    ref
                        .read(mapLocationProvider.notifier)
                        .updateMarkerPosition(defaultLocation);
                    addressController.getAddressFromLatLng(defaultLocation);
                  },
                  child: const Text('Simulate Location Selection'),
                ),
              ],
            ),
          ),
          // My location button (simulated)
          Positioned(
            bottom: 88,
            right: 16,
            child: Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
              ),
              child: InkWell(
                onTap: () {
                  // Simulate getting current location with default coordinates
                  final defaultLocation = const LatLng(24.7136, 46.6753);
                  ref
                      .read(mapLocationProvider.notifier)
                      .updateMarkerPosition(defaultLocation);
                  addressController.getAddressFromLatLng(defaultLocation);
                },
                child: SvgPicture.asset(AppIcons.gps),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/features/auction/data/models/auction.dart';
import 'package:zod/features/auction/data/models/auction_meta.dart';
import 'package:zod/features/auction/presentation/providers/auction_provider.dart';
import 'package:zod/features/auction/presentation/views/auction_times_up_widget.dart';
import 'package:zod/features/auction/presentation/views/auction_winner_section_widget.dart';
import 'package:zod/features/auction/presentation/views/auction_result_message_widget.dart';
import 'package:zod/features/auction/presentation/views/auction_points_summary_widget.dart';
import 'package:zod/features/auction/presentation/views/auction_remaining_points_widget.dart';
import 'package:zod/features/auth/presentation/providers/me_provider.dart';

class AuctionResultView extends ConsumerWidget {
  final Auction auction;
  final AuctionMeta? auctionMeta;

  const AuctionResultView({
    super.key,
    required this.auction,
    this.auctionMeta,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.watch(meProvider);
    ref.watch(auctionProvider(auction.id));
    return Container(
      color: AppColors.white,
      child: SingleChildScrollView(
        child: (auction.prizeExpired ?? false)
            ? Column(
                children: [
                  Visibility(
                      visible: auction.prizeExpired ?? false,
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: const AuctionTimesUp(),
                      )),
                ],
              )
            : Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  AuctionWinnerSectionWidget(
                      auction: auction, auctionMeta: auctionMeta),

                  // Result message (Better luck or Congratulations)
                  AuctionResultMessageWidget(
                      auction: auction, auctionMeta: auctionMeta),
                  const SizedBox(height: 14),
                  // Points summary section
                  AuctionPointsSummaryWidget(
                      auction: auction, auctionMeta: auctionMeta),
                  const SizedBox(height: 14),
                  // Remaining points section
                  AuctionRemainingPointsWidget(
                      auction: auction, auctionMeta: auctionMeta),
                ],
              ),
      ),
    );
  }
}

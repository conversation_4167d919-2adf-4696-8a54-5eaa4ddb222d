import 'dart:async';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/app_theme.dart';
import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/core/resources/app_icons.dart';
import 'package:zod/core/utils/timezone_utils.dart';

import 'package:zod/features/auction/data/models/auction.dart';
import 'package:zod/features/auction/data/models/bid_history.dart';
import 'package:zod/features/auction/presentation/providers/auction_provider.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:zod/features/auth/auth.dart';

class BidHistoryView extends ConsumerWidget {
  final Auction auction;

  const BidHistoryView({
    super.key,
    required this.auction,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final auctionState = ref.watch(auctionProvider(auction.id));
    final auctionNotifier = ref.read(auctionProvider(auction.id).notifier);

    if (auctionState.isLoadingBids) {
      return Center(
        child: CircularProgressIndicator(),
      );
    }

    // if (auctionState.bidError != null) {
    //   return Center(
    //     child: Column(
    //       mainAxisAlignment: MainAxisAlignment.center,
    //       children: [
    //         Text(
    //           auctionState.bidError!,
    //           style: TextStyle(color: Colors.red),
    //           textAlign: TextAlign.center,
    //         ),
    //         SizedBox(height: 16),
    //         ElevatedButton(
    //           onPressed: () => auctionNotifier.loadAuction(),
    //           child: Text(context.localizations.retry),
    //         ),
    //       ],
    //     ),
    //   );
    // }

    if (auctionState.bids.isEmpty) {
      return Column(
        // mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset(AppIcons.noBidYet),
          SizedBox(height: 16),
          Text(
            context.localizations.no_bids_yet,
            style: Theme.of(context).textTheme.titleMedium,
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 8),
          Text(
            context.localizations.be_the_first_to_bid,
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
        ],
      );
    }

    return NotificationListener<ScrollNotification>(
      onNotification: (ScrollNotification scrollInfo) {
        if (scrollInfo.metrics.pixels == scrollInfo.metrics.maxScrollExtent &&
            !auctionState.isLoadingMoreBids &&
            auctionState.nextBidUrl != null) {
          // auctionNotifier.loadMoreBids();
          return true;
        }
        return false;
      },
      child: ListView.builder(
        padding: EdgeInsets.all(16),
        itemCount: auctionState.bids.length,
        itemBuilder: (context, index) {
          if (index == auctionState.bids.length) {
            return Center(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: CircularProgressIndicator(),
              ),
            );
          }

          final bid = auctionState.bids[index];
          return _BidHistoryItem(
            bid: bid,
            isLatest: index == 0,
            index: index,
            needReclaimNow: auctionState.needReclaimNow,
          );
        },
      ),
    );
  }
}

class _BidHistoryItem extends StatefulWidget {
  final BidHistory bid;
  final bool isLatest;
  final bool needReclaimNow;
  final int index;

  const _BidHistoryItem({
    required this.bid,
    this.isLatest = false,
    this.needReclaimNow = false,
    this.index = 0,
  });

  @override
  State<_BidHistoryItem> createState() => _BidHistoryItemState();
}

class _BidHistoryItemState extends State<_BidHistoryItem> {
  late Timer _timer;
  late Duration _timeDifference;
  bool _isLessThanMinute = false;
  int _secondsDifference = 0;
  bool _isJustNow = false;
  bool _isOneSecond = false;

  @override
  void initState() {
    super.initState();
    _updateTimeDifference();

    // Update more frequently for recent bids (every second if less than a minute old)
    final now = TimezoneUtils.getCurrentLocalTime();
    final difference = now.difference(widget.bid.createdAt);
    final updateInterval = difference.inMinutes < 1
        ? 1
        : 20; // 1 second for recent bids, 20 seconds otherwise

    _timer = Timer.periodic(Duration(seconds: updateInterval), (_) {
      if (mounted) {
        setState(() {
          _updateTimeDifference();
        });
      }
    });
  }

  void _updateTimeDifference() {
    // Use TimezoneUtils to get the current time in the app's timezone
    final now = TimezoneUtils.getCurrentLocalTime();
    _timeDifference = now.difference(widget.bid.createdAt);

    // Store the time difference information for formatting in build
    _isLessThanMinute = _timeDifference.inMinutes < 1;
    _secondsDifference = _timeDifference.inSeconds;
    _isJustNow = _secondsDifference < 5;
    _isOneSecond = _secondsDifference == 1;
  }

  String _getFormattedDate(BuildContext context) {
    // If less than a minute old, show seconds
    if (_isLessThanMinute) {
      if (_isJustNow) {
        return context.localizations.justNow;
      } else if (_isOneSecond) {
        return context.localizations.oneSecondAgo;
      } else {
        return '$_secondsDifference ${context.localizations.differenceInsecondsSecondsAgo}';
      }
    } else {
      return timeago.format(widget.bid.createdAt, locale: 'en');
    }
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  bool get isMyBid => widget.bid.user?.id == Auth.user().id;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          margin: EdgeInsets.only(bottom: 2),
          padding: EdgeInsets.symmetric(horizontal: 12, vertical: 10),
          decoration: BoxDecoration(
            color: widget.isLatest
                ? AppColors.wornBackground
                : isMyBid
                    ? AppColors.mybidBackground
                    : Colors.white,
            borderRadius: widget.isLatest ? BorderRadius.circular(12) : null,
            border: Border.all(
              color: widget.isLatest ? AppColors.goldenrod : Colors.transparent,
              width: widget.isLatest ? 2 : 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (widget.isLatest)
                Row(
                  children: [
                    Text(
                      context.localizations.highestBid,
                      style: Theme.of(context)
                          .textTheme
                          .bodySmall
                          ?.medium
                          .slateGray,
                    ),
                    Spacer(),
                    if (widget.isLatest && isMyBid)
                      Container(
                          margin: EdgeInsets.only(bottom: 4),
                          padding:
                              EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(20),
                            color: AppColors.goldenrod,
                          ),
                          child: Row(
                            children: [
                              SvgPicture.asset(
                                AppIcons.king,
                                width: 10,
                                height: 10,
                              ),
                              SizedBox(
                                width: 3,
                              ),
                              Text(context.localizations.youAreLeading,
                                  style: Theme.of(context)
                                      .textTheme
                                      .labelMedium
                                      ?.white),
                            ],
                          )),
                  ],
                ),
              SizedBox(height: 2),
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    "${widget.index + 1}",
                    style: Theme.of(context)
                        .textTheme
                        .labelMedium
                        ?.medium
                        .slateGray,
                  ),
                  SizedBox(width: 8),

                  Stack(
                    children: [
                      CircleAvatar(
                        radius: 20,
                        backgroundImage: CachedNetworkImageProvider(
                            widget.bid.user?.image ?? ""),
                      ),
                      //add ico SvgPicture.asset(AppIcons.king),
                      if (widget.isLatest)
                        Positioned(
                          right: 0,
                          top: 0,
                          child: Container(
                              padding: EdgeInsets.all(3),
                              decoration: BoxDecoration(
                                color: AppColors.wornBackground,
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: SvgPicture.asset(
                                AppIcons.king,
                                color: AppColors.goldenrod,
                              )),
                        ),
                      if (widget.needReclaimNow && isMyBid)
                        Positioned(
                          right: 0,
                          top: 0,
                          child: Container(
                              padding: EdgeInsets.all(3),
                              decoration: BoxDecoration(
                                color: AppColors.redBackground2,
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: SvgPicture.asset(
                                AppIcons.auctionFeaild,

                                // color: AppColors.goldenrod,
                              )),
                        ),
                    ],
                  ),

                  SizedBox(width: 12),

                  // Bid details
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  widget.bid.user?.name ?? 'Unknown User',
                                  style: Theme.of(context)
                                      .textTheme
                                      .titleSmall
                                      ?.medium,
                                ),
                                SizedBox(height: 4),
                                Text(
                                  _getFormattedDate(context),
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodySmall
                                      ?.slateGray,
                                ),
                              ],
                            ),
                            Row(
                              children: [
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: [
                                    Row(
                                      children: [
                                        SvgPicture.asset(AppIcons.coin),
                                        SizedBox(
                                          width: 4,
                                        ),
                                        Text(
                                          '${widget.bid.totalBids.toInt()}',
                                          style: Theme.of(context)
                                              .textTheme
                                              .labelLarge
                                              ?.primaryColor
                                              .w700,
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        if (!widget.isLatest)
          Divider(
            color: AppColors.lightGray,
            thickness: 1,
            height: 1,
          ),
      ],
    );
  }
}

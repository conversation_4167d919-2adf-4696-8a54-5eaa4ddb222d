import 'package:bond_network/bond_network.dart';
import 'package:json_annotation/json_annotation.dart';

part 'payment_result.g.dart';

@JsonSerializable(explicitToJson: true)
class PaymentResult extends Model {
  final String status;
  final String message;

  const PaymentResult({
    required super.id,
    required this.status,
    required this.message,
  });

  factory PaymentResult.fromJson(Map<String, dynamic> json) =>
      _$PaymentResultFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$PaymentResultToJson(this);

  @override
  List<Object?> get props => super.props + [
        status,
        message,
      ];
}

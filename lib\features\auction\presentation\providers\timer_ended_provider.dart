import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/features/auction/presentation/providers/timer_provider.dart';

/// Provider that tracks whether the timer has ended or is not in the last hour
final timerEndedProvider = Provider.family<bool, AuctionTimerParams>(
  (ref, params) {
    final timerState = ref.watch(auctionCountdownTimerProvider(params));
    final totalRemainingSeconds = timerState.remainingTime.inSeconds;

    // Timer has ended or is not in the last hour (more than 59 minutes and 59 seconds remaining)
    return totalRemainingSeconds <= 0 ||
        !timerState.isVisible ||
        timerState.hasTriggeredEndEvent ||
        totalRemainingSeconds >
            3599; // 59 minutes and 59 seconds = 3599 seconds
  },
);

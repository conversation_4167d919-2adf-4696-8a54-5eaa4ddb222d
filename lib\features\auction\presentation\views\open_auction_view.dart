import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/resources/app_icons.dart';
import 'package:zod/features/auction/presentation/providers/open_auctions_provider.dart';
import 'package:zod/features/auction/presentation/providers/switch_joined_open_auction_provider.dart';
import 'package:zod/features/auction/presentation/views/shimmer_auctions_list.dart';
import 'package:zod/features/auth/auth.dart';

import 'auction_item_view.dart';
import 'empty_auction_view.dart';

class OpenAuctionsView extends ConsumerWidget {
  const OpenAuctionsView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final switchValue = ref.watch(switchJoinedOpenAuctionProvider);
    final userId = switchValue ? Auth.user().id.toString() : '';

    final openAuctions = ref.watch(openAuctionsProvider(userId));

    return openAuctions.when(
      data: (auctions) {
        log("refreshed auctions");
        return auctions.data.data.isNotEmpty
            ? ListView.separated(
                separatorBuilder: (BuildContext context, int index) {
                  return SizedBox(height: 16);
                },
                shrinkWrap: true,
                itemBuilder: (context, index) => AuctionItemView(
                  auction: auctions.data.data[index],
                ),
                itemCount: auctions.data.data.length,
              )
            : Center(
                child: EmptyAuctionsView(
                icon: AppIcons.emptyAuctions,
                title: context.localizations.no_auctions,
                description: context.localizations.start_first_bid,
              ));
      },
      error: (e, s) {
        log("s $s");
        return Center(child: Text(e.toString()));
      },
      loading: () => ShimmerAuctionsList(),
    );
  }
}

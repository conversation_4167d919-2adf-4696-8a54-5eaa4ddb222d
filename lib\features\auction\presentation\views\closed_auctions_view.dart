import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/features/auction/presentation/views/shimmer_auctions_list.dart';

import 'package:zod/core/resources/app_icons.dart';
import '../providers/closed_auctions_provider.dart';
import 'auction_item_view.dart';
import 'empty_auction_view.dart';

class ClosedAuctionsView extends ConsumerWidget {
  const ClosedAuctionsView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final closedAuctions = ref.watch(closedAuctionsProvider);
    return closedAuctions.when(
      data: (auctions) {
        return auctions.isNotEmpty
            ? ListView.separated(
                separatorBuilder: (BuildContext context, int index) {
                  return SizedBox(height: 16);
                },
                shrinkWrap: true,
                itemBuilder: (context, index) =>
                    AuctionItemView(auction: auctions[index]),
                itemCount: auctions.length,
              )
            : Center(
                child: EmptyAuctionsView(
                  icon: AppIcons.emptyAuctions,
                  title: context.localizations.no_auctions,
                  description: context.localizations.start_first_bid,
                ),
              );
      },
      error: (e, s) {
        log("e ${e.toString()}", stackTrace: s);
        return Center(child: Text(e.toString()));
      },
      loading: () => ShimmerAuctionsList(),
    );
  }
}

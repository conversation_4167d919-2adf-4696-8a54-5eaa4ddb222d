import 'dart:developer' as dev;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/core/utils/timezone_utils.dart';
import 'package:zod/features/auction/data/models/auction.dart';
import 'package:zod/features/auction/presentation/providers/auction_last_hour_provider.dart';
import 'package:zod/features/auction/presentation/providers/auction_time_listener_provider.dart';

/// Provider that determines whether the HurryUpView should be visible
///
/// The HurryUpView should be visible when:
/// 1. The auction has a valid end time
/// 2. The auction is in its last hour (59 minutes and 59 seconds or less remaining)
///    OR the auction_last_hour event has been received from Pusher
/// 3. The user has joined the auction
///
/// Note: The HurryUpView widget itself will handle hiding when the timer ends
///
/// IMPORTANT: We've removed the status check because the hurry up notification
/// should be shown based on time remaining, not the auction status
/// IMPORTANT: This provider has been modified to ALWAYS show the hurry up notification
/// when the auction is in its last hour and the user has joined, regardless of any other conditions.
final hurryUpVisibilityProvider =
    Provider.family<bool, Auction?>((ref, auction) {
  // DIRECT CHECK: If the auction is in its last hour and the user has joined, show the hurry up notification
  if (auction != null && auction.endedAt != null) {
    final isInLastHour = auction.isInLastHour;
    final userHasJoined =
        auction.isJoined == true || auction.hasCurrentUserJoined;

    if (isInLastHour && userHasJoined) {
      dev.log(
          'HURRY_UP_VISIBILITY - DIRECT CHECK: Auction is in last hour and user has joined');
      dev.log(
          'HURRY_UP_VISIBILITY - DIRECT CHECK: Forcing hurry up view to show');
      return true;
    }
  }
  // If auction is null or has no end time, don't show the HurryUpView
  if (auction == null || auction.endedAt == null) {
    dev.log('HURRY_UP_VISIBILITY - Auction is null or has no end time');
    return false;
  }

  // Log auction ID
  dev.log('HURRY_UP_VISIBILITY - Auction ID: ${auction.id}');

  // Check if the user has joined the auction
  // We check both the isJoined flag and the hasCurrentUserJoined method
  final isJoinedFlag = auction.isJoined == true;
  final hasJoined = auction.hasCurrentUserJoined;
  final userHasJoined = isJoinedFlag || hasJoined;

  dev.log('HURRY_UP_VISIBILITY - isJoined flag: $isJoinedFlag');
  dev.log('HURRY_UP_VISIBILITY - hasCurrentUserJoined: $hasJoined');
  dev.log('HURRY_UP_VISIBILITY - userHasJoined (combined): $userHasJoined');

  // Check if the auction is in its last hour based on time calculation
  final isInLastHour = auction.isInLastHour;
  dev.log('HURRY_UP_VISIBILITY - isInLastHour: $isInLastHour');

  // Get the end time and calculate remaining time
  if (auction.endedAt != null) {
    final now = TimezoneUtils.getCurrentLocalTime();
    final differenceInSeconds =
        TimezoneUtils.getSecondsDifference(auction.endedAt!, now);
    final hours = differenceInSeconds ~/ 3600;
    final minutes = (differenceInSeconds % 3600) ~/ 60;
    final seconds = differenceInSeconds % 60;

    dev.log('HURRY_UP_VISIBILITY - End time: ${auction.endedAt}');
    dev.log('HURRY_UP_VISIBILITY - Current time: $now');
    dev.log(
        'HURRY_UP_VISIBILITY - Difference in seconds: $differenceInSeconds');
    dev.log(
        'HURRY_UP_VISIBILITY - Remaining time - Hours: $hours, Minutes: $minutes, Seconds: $seconds');
  }

  // Also check if the auction_last_hour event has been received from Pusher
  final isLastHourFromPusher = ref.watch(auctionLastHourProvider(auction));
  dev.log('HURRY_UP_VISIBILITY - isLastHourFromPusher: $isLastHourFromPusher');

  // Check if the time listener has detected that the auction is in its last hour
  final isInLastHourFromListener =
      ref.watch(auctionTimeListenerProvider(auction));
  dev.log(
      'HURRY_UP_VISIBILITY - isInLastHourFromListener: $isInLastHourFromListener');

  // The auction is in its last hour if any of the following conditions are true:
  // 1. The time calculation says so
  // 2. The Pusher event has been received
  // 3. The time listener has detected that the auction is in its last hour
  final isInFinalHour =
      isInLastHour || isLastHourFromPusher || isInLastHourFromListener;
  dev.log('HURRY_UP_VISIBILITY - isInFinalHour: $isInFinalHour');

  // Only show the HurryUpView if all conditions are met
  final result = isInFinalHour && userHasJoined;
  dev.log('HURRY_UP_VISIBILITY - Final result: $result');

  // OVERRIDE: Force it to return true if the auction is in its last hour and the user has joined
  // This is a temporary fix to ensure the hurry up notification is shown
  if ((isInLastHour || isLastHourFromPusher || isInLastHourFromListener) &&
      userHasJoined) {
    dev.log('HURRY_UP_VISIBILITY - OVERRIDE: Forcing hurry up view to show');
    dev.log('HURRY_UP_VISIBILITY - isInLastHour: $isInLastHour');
    dev.log(
        'HURRY_UP_VISIBILITY - isLastHourFromPusher: $isLastHourFromPusher');
    dev.log(
        'HURRY_UP_VISIBILITY - isInLastHourFromListener: $isInLastHourFromListener');
    dev.log('HURRY_UP_VISIBILITY - userHasJoined: $userHasJoined');
    return true;
  }

  // If we're here, it means the override didn't trigger
  // Let's log more details to understand why
  dev.log('HURRY_UP_VISIBILITY - Override not triggered:');
  dev.log('HURRY_UP_VISIBILITY - isInLastHour: $isInLastHour');
  dev.log('HURRY_UP_VISIBILITY - userHasJoined: $userHasJoined');
  dev.log('HURRY_UP_VISIBILITY - isJoinedFlag: $isJoinedFlag');
  dev.log('HURRY_UP_VISIBILITY - hasJoined: $hasJoined');

  return result;
});

import 'package:flutter/material.dart';
import 'package:zod/core/app_theme.dart';
import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/features/home/<USER>/models/address.dart';

class FormattedAddressView extends StatelessWidget {
  final Address address;

  const FormattedAddressView({
    Key? key,
    required this.address,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.borderColor),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Location icon
          const Icon(Icons.location_on, color: AppColors.royalBlue, size: 24),
          const SizedBox(width: 12),

          // Address details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Address and house number/street
                Text(
                  '${address.address}, ${address.houseNoStreet}',
                  style: Theme.of(context).textTheme.bodyMedium?.w700,
                ),
                const SizedBox(height: 4),

                // Phone number
                Text(
                  'Phone: ${address.phoneNumber}',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

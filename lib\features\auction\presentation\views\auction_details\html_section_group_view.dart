import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:html/dom.dart' as dom;
import 'package:html/parser.dart' as html_parser;
import 'package:zod/core/app_theme.dart';
import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/core/resources/app_icons.dart';

class HtmlSectionGroupView extends StatelessWidget {
  const HtmlSectionGroupView({
    super.key,
    required this.html,
    required this.svgIcons,
    this.defaultSvg = AppIcons.info,
    this.showDividerAfter = false,
  });

  final String html;
  final List<String> svgIcons;
  final String defaultSvg;
  final bool showDividerAfter;

  @override
  Widget build(BuildContext context) {
    final sections = _splitHtmlByH2(html);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ...sections.asMap().entries.map((entry) {
          final index = entry.key;
          final sectionData = _extractHeadingAndContent(entry.value);
          final svgPath = index < svgIcons.length ? svgIcons[index] : defaultSvg;

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 16),
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SvgPicture.asset(svgPath, width: 20, height: 20),
                  const SizedBox(width: 8),
                  Text(
                    sectionData['heading'],
                    style: context.textTheme.titleSmall?.w700,
                  ),
                ],
              ),
              Html(
                data: sectionData['content'],
                style: {
                  "p": Style(
                    color: AppColors.slateGray,
                    fontSize:  FontSize(14),
                    fontWeight: FontWeight.w400,
                    margin: Margins.zero,
                  ),
                  "strong": Style(
                    color: AppColors.textColor,
                    fontWeight: FontWeight.w700,
                  ),
                  "b": Style(
                    color: AppColors.textColor,
                    fontWeight: FontWeight.w700,
                  ),
                },
              ),
              if (index < sections.length - 1)
                const Divider(
                  height: 24,
                  thickness: 1,
                  color: AppColors.lightGray,
                ),
            ],
          );
        }),

        if (showDividerAfter && sections.isNotEmpty)
          const Divider(
            height: 24,
            thickness: 1,
            color: AppColors.lightGray,
          ),
      ],
    );
  }

  List<String> _splitHtmlByH2(String htmlString) {
    final document = html_parser.parse(htmlString);
    final List<String> sections = [];
    List<dom.Node> currentSectionNodes = [];

    for (var node in document.body!.nodes) {
      if (node is dom.Element &&
          (node.localName == 'h2' || node.localName == 'h3')) {
        if (currentSectionNodes.isNotEmpty) {
          final sectionHtml = currentSectionNodes
              .map((n) => n is dom.Element ? n.outerHtml : (n.text ?? ''))
              .join('');
          sections.add(sectionHtml);
          currentSectionNodes = [];
        }
      }
      currentSectionNodes.add(node);
    }

    if (currentSectionNodes.isNotEmpty) {
      final sectionHtml = currentSectionNodes
          .map((n) => n is dom.Element ? n.outerHtml : (n.text ?? ''))
          .join('');
      sections.add(sectionHtml);
    }

    return sections;
  }

  Map<String, dynamic> _extractHeadingAndContent(String section) {
    final document = html_parser.parse(section);
    String headingText = '';
    String remainingContent = '';
    bool foundHeading = false;

    for (var node in document.body!.nodes) {
      if (!foundHeading &&
          node is dom.Element &&
          (node.localName == 'h2' || node.localName == 'h3')) {
        headingText = node.text.trim();
        foundHeading = true;
      } else if (node is dom.Element && node.localName == 'p') {
        final text = node.text.trim();
        if (text.isNotEmpty && text != '&nbsp;') {
          remainingContent += node.outerHtml;
        }
      } else if (node.nodeType == dom.Node.TEXT_NODE) {
        final text = node.text?.trim();
        if (text != null && text.isNotEmpty) {
          remainingContent += text;
        }
      }
    }

    return {
      'heading': headingText,
      'content': remainingContent,
    };
  }
}

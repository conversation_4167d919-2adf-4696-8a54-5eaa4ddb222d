import 'package:bond_network/bond_network.dart';
import 'package:json_annotation/json_annotation.dart';

part 'claim_prize.g.dart';

@JsonSerializable(explicitToJson: true)
class ClaimPrize extends Model {
  final String name;
  final String image;
  @Json<PERSON>ey(name: 'final_bid_points')
  final int finalBidPoints;
  @Json<PERSON>ey(name: 'points_value')
  final int pointsValue;
  @<PERSON>son<PERSON>ey(name: 'product_cost')
  final int productCost;

  @<PERSON>son<PERSON>ey(name: 'remaining_to_pay')
  final int remainingToPay;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'tax_percentage')
  final String taxPercentage;

  @<PERSON>son<PERSON>ey(name: 'tax_amount')
  final double taxAmount;

  @Json<PERSON>ey(name: 'total_to_pay')
  final double totalToPay;

  @Json<PERSON>ey(name: 'claim_prize_before')
  final DateTime? claimBefore;

  const ClaimPrize({
    required super.id,
    required this.name,
    required this.image,
    required this.finalBidPoints,
    required this.productCost,
    required this.pointsValue,
    required this.remainingToPay,
    required this.taxPercentage,
    required this.taxAmount,
    required this.totalToPay,
    this.claimBefore,
  });

  factory ClaimPrize.fromJson(Map<String, dynamic> json) =>
      _$ClaimPrizeFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$ClaimPrizeToJson(this);

  @override
  List<Object?> get props =>
      super.props +
      [
        id,
        name,
        image,
        finalBidPoints,
        pointsValue,
        remainingToPay,
        taxPercentage,
        taxAmount,
        totalToPay,
        claimBefore,
      ];
}

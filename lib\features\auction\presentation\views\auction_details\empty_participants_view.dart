
import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/app_theme.dart';
import 'package:zod/core/resources/app_icons.dart';

import 'package:zod/core/resources/app_colors.dart';

class EmptyParticipantsView extends StatelessWidget {
  const EmptyParticipantsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppColors.white,
      child: SingleChildScrollView(
        child: Column(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(height: 30),
                SvgPicture.asset(AppIcons.emptyCompetition),
                SizedBox(height: 16),
                Text(
                    context.localizations
                        .join_the_auction_to_be_part_of_the_action,
                    style: context.textTheme.titleSmall?.w700),
                SizedBox(height: 4),
                Text(context.localizations.be_among_the_first_to_place_a_bid,
                    style: context.textTheme.bodyLarge),
                SizedBox(height: 16),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 18, vertical: 12),
                  decoration: BoxDecoration(
                    color: AppColors.primaryColor,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(context.localizations.join_the_auction,
                          style: context.textTheme.labelLarge?.white),
                      SizedBox(width: 8),
                      SvgPicture.asset(
                        AppIcons.arrow,
                        color: AppColors.white,
                        width: 20,
                        height: 20,
                      )
                    ],
                  ),
                )
              ],
            ),
          ],
        ),
      ),
    );
  }
}

import 'package:bond_core/bond_core.dart';
import 'package:bond_form/bond_form.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/features/auction/data/api.dart';
import 'package:zod/features/auction/data/models/extra_time_reason.dart';
import 'package:zod/features/auction/data/models/extra_time_reason_response.dart';

typedef DropDownItemsState<T> = List<DropDownItemState<T>>;

/// Provider that fetches the raw API response containing both reasons and hours
final extraTimeDataProvider =
    FutureProvider<ExtraTimeReasonResponse>((ref) async {
  final api = sl<AuctionApi>();
  final response = await api.reasons();
  return response.data;
});

/// Provider for reasons dropdown items
final extraTimeReasonProvider =
    FutureProvider<DropDownItemsState<ExtraTimeReason>>((ref) async {
  // Get the data from the base provider
  final responseData = await ref.watch(extraTimeDataProvider.future);

  // Map reasons to dropdown items
  final dropdownItems = responseData.reasons
      .map((reason) =>
          DropDownItemState<ExtraTimeReason>(reason, label: reason.name))
      .toList();
  return dropdownItems;
});

/// Provider for hours dropdown items
final extraTimeHoursProvider =
    FutureProvider<List<DropDownItemState<int>>>((ref) async {
  // Get the data from the base provider
  final responseData = await ref.watch(extraTimeDataProvider.future);

  // Map hours to dropdown items
  final dropdownItems = responseData.hours
      .map((hour) => DropDownItemState<int>(hour,
          label: '$hour hour${hour > 1 ? 's' : ''}'))
      .toList();
  return dropdownItems;
});

/// Helper function to get both reasons and hours
Future<Map<String, dynamic>> getExtraTimeData(WidgetRef ref) async {
  final responseData = await ref.read(extraTimeDataProvider.future);
  return {
    'reasons': responseData.reasons,
    'hours': responseData.hours,
  };
}

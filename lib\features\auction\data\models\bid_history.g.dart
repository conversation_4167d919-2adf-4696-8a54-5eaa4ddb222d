// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'bid_history.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BidHistory _$BidHistoryFromJson(Map<String, dynamic> json) => BidHistory(
      id: (json['id'] as num).toInt(),
      totalBids: (json['total_bids'] as num).toDouble(),
      user: json['user'] == null
          ? null
          : BidUser.fromJson(json['user'] as Map<String, dynamic>),
      createdAt: DateTime.parse(json['last_bid_at'] as String),
      isWinner: json['is_winner'] as bool? ?? false,
      userId: (json['user_id'] as num).toInt(),
    );

Map<String, dynamic> _$BidHistoryToJson(BidHistory instance) =>
    <String, dynamic>{
      'id': instance.id,
      'total_bids': instance.totalBids,
      'user': instance.user?.toJson(),
      'last_bid_at': instance.createdAt.toIso8601String(),
      'is_winner': instance.isWinner,
      'user_id': instance.userId,
    };

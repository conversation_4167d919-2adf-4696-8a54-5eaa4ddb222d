import 'package:zod/features/auction/data/models/claim_prize.dart';
import 'package:zod/features/home/<USER>/models/address.dart';

class ClaimPrizeState {
  final ClaimPrize? claimPrize;
  final Address? selectedAddress;
  final bool isLoading;
  final bool isProcessingPayment;
  final bool isRequestingExtraTime;
  final bool paymentCompleted;
  final String? error;

  ClaimPrizeState({
    this.claimPrize,
    this.selectedAddress,
    required this.isLoading,
    required this.isProcessingPayment,
    required this.isRequestingExtraTime,
    required this.paymentCompleted,
    this.error,
  });

  factory ClaimPrizeState.initial() => ClaimPrizeState(
        claimPrize: null,
        selectedAddress: null,
        isLoading: false,
        isProcessingPayment: false,
        isRequestingExtraTime: false,
        paymentCompleted: false,
        error: null,
      );

  ClaimPrizeState copyWith({
    ClaimPrize? claimPrize,
    Address? selectedAddress,
    bool? isLoading,
    bool? isProcessingPayment,
    bool? isRequestingExtraTime,
    bool? paymentCompleted,
    String? error,
  }) {
    return ClaimPrizeState(
      claimPrize: claimPrize ?? this.claimPrize,
      selectedAddress: selectedAddress ?? this.selectedAddress,
      isLoading: isLoading ?? this.isLoading,
      isProcessingPayment: isProcessingPayment ?? this.isProcessingPayment,
      isRequestingExtraTime:
          isRequestingExtraTime ?? this.isRequestingExtraTime,
      paymentCompleted: paymentCompleted ?? this.paymentCompleted,
      error: error,
    );
  }

  T when<T>({
    required T Function() loading,
    required T Function(String error) error,
    required T Function(ClaimPrize claimPrize) data,
  }) {
    if (isLoading) {
      return loading();
    } else if (this.error != null) {
      return error(this.error!);
    } else if (claimPrize != null) {
      return data(claimPrize!);
    } else {
      return error('No data available');
    }
  }
}

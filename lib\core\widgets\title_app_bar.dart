import 'dart:math' as math;

import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:simple_shadow/simple_shadow.dart';
import 'package:zod/app/routes.dart';
import 'package:zod/core/resources/app_icons.dart';
import 'package:zod/core/widgets/network_image_widget.dart';
import 'package:zod/features/auction/presentation/providers/open_auctions_provider.dart';
import 'package:zod/features/auction/presentation/providers/switch_joined_open_auction_provider.dart';
import 'package:zod/features/auth/auth.dart';

import '../resources/app_colors.dart';

class TitleAppBar extends ConsumerWidget implements PreferredSizeWidget {
  const TitleAppBar({
    super.key,
    this.extendBodyBehindAppBar = false,
    this.backgroundColor = AppColors.lavenderMist,
    required this.title,
    this.hideBack = false,
    this.showImageProfile = false,
    this.showSwitchOpenAuction = false,
    this.actions = const [],
  });

  final bool extendBodyBehindAppBar;
  final Color backgroundColor;
  final String title;
  final bool hideBack;
  final bool showImageProfile;
  final bool showSwitchOpenAuction;
  final List<Widget> actions;

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final switchValue = ref.watch(switchJoinedOpenAuctionProvider);
    bool isRTL = Directionality.of(context) == TextDirection.rtl;
    return AppBar(
      backgroundColor: backgroundColor,
      centerTitle: false,
      automaticallyImplyLeading: false,
      leadingWidth: 0,
      titleSpacing: 0,
      title: Row(
        children: [
          Visibility(
            visible: !hideBack,
            child: Transform.translate(
              offset: Offset(0, isRTL ? -5 : 5),
              child: InkWell(
                highlightColor: Colors.transparent,
                splashColor: Colors.transparent,
                onTap: () => goRouter.pop(),
                child: SimpleShadow(
                  color: Colors.black,
                  opacity: 0.06,
                  offset: Offset(0, 6),
                  sigma: 10,
                  child: Transform.rotate(
                    angle: isRTL ? math.pi : 0,
                    child: SvgPicture.asset(AppIcons.backArrow),
                  ),
                ),
              ),
            ),
          ),
          Visibility(visible: hideBack, child: SizedBox(width: 16)),
          Text(title, style: context.textTheme.titleLarge),
        ],
      ),
      // actionsPadding: EdgeInsetsDirectional.only(end: 20),
      actions: [
        if (Auth.check())
          Visibility(
              visible: showImageProfile,
              //radius: 18,
              child: CircleAvatar(
                radius: 36 / 2,
                backgroundColor: AppColors.periwinkle,
                // Child circle avatar
                child: CircleAvatar(
                  radius: 30 / 2,
                  backgroundImage: NetworkImage(Auth.user().image ??
                      'https://avatar.iran.liara.run/public/25'),
                  // child: NetworkImageView(
                  //   entry.value.avatar?.url ?? "",
                  //   width: 20,
                  //   height: 20,
                  //   borderColor: AppColors.white,
                  //   radius: 11.21,
                  // ),
                  // backgroundImage: NetworkImage(entry.value?.avatar?.url),
                ),
              )
              // NetworkImageView(
              //   Auth.user().image ?? 'https://avatar.iran.liara.run/public/25',
              //   height: 36,
              //   width: 36,
              //   radius: 18,
              //   borderColor: AppColors.periwinkle,
              // ),
              ),
        SizedBox(width: 20),
        if (Auth.check())
          Visibility(
            visible: showSwitchOpenAuction && Auth.check(),
            child: Switch(
              value: switchValue,
              onChanged: (value) {
                ref.read(switchJoinedOpenAuctionProvider.notifier).state =
                    value;
                if (value && Auth.check()) {
                  ref.refresh(openAuctionsProvider(Auth.user().id.toString()));
                } else {
                  ref.invalidate(openAuctionsProvider(''));
                }
              },
              padding: EdgeInsetsDirectional.only(start: 3, top: 2, bottom: 2),
              activeColor: AppColors.white,
              activeTrackColor: AppColors.activeGreen,
              inactiveThumbColor: AppColors.white,
              inactiveTrackColor: AppColors.lightGray,
              trackOutlineColor: WidgetStateProperty.resolveWith<Color?>(
                  (states) => Colors.transparent),
            ),
          ),
        SizedBox(width: 16),
        ...actions,
      ],
    );
  }
}

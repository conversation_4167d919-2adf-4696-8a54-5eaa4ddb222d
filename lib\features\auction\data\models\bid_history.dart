import 'package:bond_network/bond_network.dart';
import 'package:json_annotation/json_annotation.dart';

import 'bid_user.dart';

part 'bid_history.g.dart';

@JsonSerializable(explicitToJson: true)
class BidHistory extends Model {
  @Json<PERSON>ey(name: 'total_bids')
  final double totalBids;
  final BidUser? user;

  @Json<PERSON>ey(name: 'last_bid_at')
  final DateTime createdAt;
  @Json<PERSON>ey(name: 'is_winner', defaultValue: false)
  final bool isWinner;
  @JsonKey(name: 'user_id')
  final int userId;

  const BidHistory({
    required super.id,
    required this.totalBids,
    required this.user,
    required this.createdAt,
    required this.isWinner,
    required this.userId,
  });

  factory BidHistory.fromJson(Map<String, dynamic> json) {
    json["id"] = 0;
    return _$BidHistoryFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() => _$BidHistoryToJson(this);

  @override
  List<Object?> get props => [id];
}

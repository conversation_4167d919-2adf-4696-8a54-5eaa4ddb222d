import 'dart:async';
import 'dart:developer' as dev;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/core/utils/timezone_utils.dart';
import 'package:zod/features/auction/data/models/auction.dart';
import 'package:zod/features/auction/presentation/providers/auction_last_hour_provider.dart';
import 'package:zod/features/auction/presentation/providers/hurry_up_visibility_provider.dart';

/// A provider that listens to time changes and updates the hurry up visibility
/// when an auction enters its last hour
final auctionTimeListenerProvider = StateNotifierProvider.family<
    AuctionTimeListenerNotifier, bool, Auction>(
  (ref, auction) => AuctionTimeListenerNotifier(ref, auction),
);

class AuctionTimeListenerNotifier extends StateNotifier<bool> {
  final Ref _ref;
  final Auction _auction;
  Timer? _timer;

  AuctionTimeListenerNotifier(this._ref, this._auction) : super(false) {
    // Initialize state based on whether the auction is in its last hour
    final isInLastHour = _auction.isInLastHour;
    // dev.log('AUCTION_TIME_LISTENER - Initial isInLastHour: $isInLastHour');
    
    // Set initial state
    state = isInLastHour;
    
    // Start timer to check time changes
    _startTimer();
  }

  void _startTimer() {
    // Cancel any existing timer
    _timer?.cancel();
    
    // Check every second if the auction has entered its last hour
    _timer = Timer.periodic(const Duration(seconds: 1), (_) {
      _checkTimeStatus();
    });
  }

  void _checkTimeStatus() {
    // If auction has no end time, return
    if (_auction.endedAt == null) {
      // dev.log('AUCTION_TIME_LISTENER - Auction has no end time');
      return;
    }
    
    // Get current time
    final now = TimezoneUtils.getCurrentLocalTime();
    
    // Calculate remaining time
    final differenceInSeconds = 
        TimezoneUtils.getSecondsDifference(_auction.endedAt!, now);
    
    // Calculate hours, minutes, and seconds
    final hours = differenceInSeconds ~/ 3600;
    final minutes = (differenceInSeconds % 3600) ~/ 60;
    final seconds = differenceInSeconds % 60;
    
    // Log time values
    // dev.log('AUCTION_TIME_LISTENER - Auction ID: ${_auction.id}');
    // dev.log('AUCTION_TIME_LISTENER - Remaining time - Hours: $hours, Minutes: $minutes, Seconds: $seconds');
    
    // Check if auction is in its last hour (using the same threshold as in Auction.isInLastHour)
    final isInLastHour = differenceInSeconds <= 3605 && differenceInSeconds > 0;
    
    // If auction has entered its last hour and state is false, update state
    if (isInLastHour && !state) {
      // dev.log('AUCTION_TIME_LISTENER - Auction has entered its last hour');
      state = true;
      
      // Update the auctionLastHourProvider
      _ref.read(auctionLastHourProvider(_auction).notifier).state = true;
      
      // Force a rebuild of the hurryUpVisibilityProvider
      _ref.invalidate(hurryUpVisibilityProvider(_auction));
    }
    
    // If auction is no longer in its last hour and state is true, update state
    if (!isInLastHour && state) {
      // dev.log('AUCTION_TIME_LISTENER - Auction is no longer in its last hour');
      state = false;
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }
}

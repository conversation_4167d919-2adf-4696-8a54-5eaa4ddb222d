import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart' show SvgPicture;
import 'package:skeletonizer/skeletonizer.dart';
import 'package:zod/core/localizations/app_localizations_extension.dart';
import 'package:zod/core/resources/app_icons.dart';

class FooterNotWinnerItemView extends StatelessWidget {
  const FooterNotWinnerItemView({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Skeleton.ignore(child: SvgPicture.asset(AppIcons.sad)),
        SizedBox(width: 8),
        Text(
          context.localizations.better_luck_next_time,
          style: context.textTheme.labelLarge,
        ),
      ],
    );
  }
}

import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/app/routes.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/app_theme.dart';
import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/core/widgets/app_button.dart';
import 'package:zod/features/auction/data/models/auction.dart';
import 'package:zod/features/auction/data/states/auction_state.dart';

class CompetitionGestLiveBottomNavView extends ConsumerWidget {
  const CompetitionGestLiveBottomNavView(
      {required this.auction, required this.auctionState, super.key});
  final AuctionState auctionState;
  final Auction auction;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
            Container(
              padding: EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              decoration: BoxDecoration(
                color: AppColors.softWhite,
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.info_outline,
                      size: 20, color: AppColors.primaryColor),
                  SizedBox(width: 4),
              Text(context.localizations.auctionStartedJoinEarlyNextTimeSoYouDonTMissOut,
                          style: context.textTheme.labelMedium?.medium),
                  SizedBox(width: 8),
             
                ],
              )),
       
     
        Container(
          padding: EdgeInsets.only(left: 16, right: 16, top: 12, bottom: 20),
          decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(10),
                topRight: Radius.circular(10),
              ),
              boxShadow: [
                BoxShadow(
                  blurRadius: 8,
                  offset: Offset(0, -2),
                  color: AppColors.blackShadow,
                ),
              ]),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Expanded(
                    child: AppButton(
                      title: context.localizations.exploreWhatComing,
                      textColor: AppColors.steelGray,
                      backgroundColor: AppColors.disabled,
                      borderColor: AppColors.softSteel,
                      onPressed: () {
                        goRouter.go('/home');
                      },
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }
}

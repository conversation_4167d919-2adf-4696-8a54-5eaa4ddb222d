import 'dart:developer';

import 'package:zod/core/analytics/events/events.dart';
import 'package:zod/core/app_analytics.dart';
import 'package:zod/features/auction/data/models/auction.dart';

/// Track when a user signs up for an auction
void trackAuctionSignup(Auction auction) {

  
  AppAnalytics.fire(AuctionSignupEvent(
    auctionId: auction.id,
    auctionName: auction.name,
    entryFee: auction.entryFee?.toDouble() ?? 0,
  ));
  
}

import 'package:flutter/material.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:zod/features/auction/presentation/views/auction_item_view.dart';

import '../../../../core/resources/app_colors.dart';
import '../../data/models/auction.dart';

class ShimmerAuctionsList extends StatelessWidget {
  const ShimmerAuctionsList({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Skeletonizer(
        containersColor: AppColors.lightGray,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ListView.separated(
                separatorBuilder: (BuildContext context, int index) {
                  return SizedBox(height: 16);
                },
                shrinkWrap: true,
                itemCount: Auction.fakeList.length,
                itemBuilder: (context, index) {
                  final auction = Auction.fakeList[index];
                  return AuctionItemView(
                    auction: auction,
                  );
                },
              ),
            ],
          )),
    );
  }
}

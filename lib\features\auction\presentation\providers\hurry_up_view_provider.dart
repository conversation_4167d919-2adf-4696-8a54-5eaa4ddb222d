import 'dart:developer' as dev;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/features/auction/data/models/auction.dart';
import 'package:zod/features/auction/presentation/providers/timer_provider.dart';

/// Provider that determines whether to show the HurryUpView
final hurryUpViewProvider = Provider.family<bool, Auction>((ref, auction) {
  // Check if the auction has a valid end time
  if (auction.endedAt == null) {
    return false;
  }

  // Check if the auction is in its last hour using the isInLastHour method
  final isInLastHour = auction.isInLastHour;

  if (!isInLastHour) {
    return false;
  }

  // Create the timer parameters with the auction's end time
  final params = (endTime: auction.endedAt!, auctionId: auction.id);

  // Watch the timer state
  final timerState = ref.watch(auctionCountdownTimerProvider(params));

  // Get the remaining time in seconds, hours, and minutes
  final totalRemainingSeconds = timerState.remainingTime.inSeconds;
  final remainingHours = timerState.remainingTime.inHours;
  final remainingMinutes = (timerState.remainingTime.inMinutes % 60);


  // We want to show the HurryUpView as long as there's time remaining, even if it's 00:00:59

  // Check each condition separately
  final isTimerEnded = totalRemainingSeconds <= 0;
  final isTimerNotVisible = !timerState.isVisible;
  final hasTriggeredEnd = timerState.hasTriggeredEndEvent;

  // Check if we should show the HurryUpView based on time
  // We want to show it when the timer is at 59:59 or less
  // The condition is: total seconds <= 3599 (59 minutes and 59 seconds) and > 0
  final shouldShowBasedOnTime =
      totalRemainingSeconds <= 3599 && totalRemainingSeconds > 0;


  // Only hide when timer has ended or is not visible
  if (isTimerEnded || isTimerNotVisible || hasTriggeredEnd) {
    return false;
  }

  // Make sure we're showing it when time is 59:59 or less
  return shouldShowBasedOnTime;
});

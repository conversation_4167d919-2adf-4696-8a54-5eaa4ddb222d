import 'package:bond_core/bond_core.dart';
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:zod/features/auction/data/models/auction_result.dart';

part 'auction_meta.g.dart';

@JsonSerializable(explicitToJson: true)
class AuctionMeta extends Equatable with Jsonable {
  final bool? success;
  final String? message;
  @Json<PERSON>ey(name: 'bids_count')
  final int? bidsCount;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'break_until')
  final DateTime? breakUntil;

  final AuctionResult? result;

  const AuctionMeta({
    this.success,
    required this.message,
    this.bidsCount,
    this.breakUntil,
    this.result,
  });

  factory AuctionMeta.fromJson(Map<String, dynamic> json) =>
      _$AuctionMetaFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$AuctionMetaToJson(this);

  @override
  List<Object?> get props => [message, success, bidsCount, result];
}

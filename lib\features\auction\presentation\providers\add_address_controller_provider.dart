import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:geocoding/geocoding.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:custom_info_window/custom_info_window.dart';
import 'package:bond_form/bond_form.dart';
import 'package:zod/features/auction/presentation/providers/add_address_form_provider.dart';
import 'package:zod/features/auction/presentation/providers/map_location_provider.dart';
import 'package:zod/features/auction/presentation/views/custom_info_window_view.dart';

// State class for the add address controller
class AddAddressState {
  // Map related properties
  final GoogleMapController? mapController;
  final CustomInfoWindowController customInfoWindowController;
  final bool isInfoWindowVisible;

  // Address related properties
  final String? currentAddress;
  final String? streetName;
  final String? cityName;
  final String? countryName;

  // Loading state
  final bool isLoading;

  // Marker visibility state
  final bool markersInitialized;

  AddAddressState({
    this.mapController,
    this.currentAddress,
    this.streetName,
    this.cityName,
    this.countryName,
    required this.customInfoWindowController,
    this.isInfoWindowVisible = false,
    this.isLoading = false,
    this.markersInitialized = false,
  });

  // Create a copy of the state with updated values
  AddAddressState copyWith({
    GoogleMapController? mapController,
    String? currentAddress,
    String? streetName,
    String? cityName,
    String? countryName,
    CustomInfoWindowController? customInfoWindowController,
    bool? isInfoWindowVisible,
    bool? isLoading,
    bool? markersInitialized,
  }) {
    return AddAddressState(
      mapController: mapController ?? this.mapController,
      currentAddress: currentAddress ?? this.currentAddress,
      streetName: streetName ?? this.streetName,
      cityName: cityName ?? this.cityName,
      countryName: countryName ?? this.countryName,
      customInfoWindowController:
          customInfoWindowController ?? this.customInfoWindowController,
      isInfoWindowVisible: isInfoWindowVisible ?? this.isInfoWindowVisible,
      isLoading: isLoading ?? this.isLoading,
      markersInitialized: markersInitialized ?? this.markersInitialized,
    );
  }

  // Helper method to get formatted address
  String get formattedAddress {
    final List<String> parts = [
      streetName ?? '',
      cityName ?? '',
      countryName ?? ''
    ].where((part) => part.isNotEmpty).toList();

    log("ssss parts ${parts}");
    log("ssss streetName ${streetName}");
    log("ssss cityName ${cityName}");
    log("ssss countryName ${countryName}");

    final result = parts.isNotEmpty ? parts.join(', ') : 'Location';
    log("formattedAddress getter returning: $result");
    return result;
  }
}

// Provider for the add address controller
final addAddressControllerProvider =
    StateNotifierProvider<AddAddressController, AddAddressState>((ref) {
  return AddAddressController(ref);
});

// Controller class for the add address functionality
class AddAddressController extends StateNotifier<AddAddressState> {
  final Ref _ref;

  // Default location for Saudi Arabia (Riyadh)
  static const LatLng defaultSaudiLocation = LatLng(24.7136, 46.6753);

  AddAddressController(this._ref)
      : super(AddAddressState(
          customInfoWindowController: CustomInfoWindowController(),
        ));

  @override
  void dispose() {
    state.mapController?.dispose();
    state.customInfoWindowController.dispose();
    super.dispose();
  }

  // Initialize the controller with default location
  void initialize() async {
    // Set loading state
    state = state.copyWith(isLoading: true);

    // Add a delay to show the shimmer effect (remove in production)
    await Future.delayed(const Duration(seconds: 2));

    // Update loading state to false after shimmer effect
    state = state.copyWith(isLoading: false, markersInitialized: true);

    // Set the default location in Saudi Arabia (Riyadh) and mark as initialized
    await _ref
        .read(mapLocationProvider.notifier)
        .updateMarkerPosition(defaultSaudiLocation);

    // Get the address for the default location
    await getAddressFromLatLng(defaultSaudiLocation);

    // Force marker to stay visible after a delay
    Future.delayed(const Duration(milliseconds: 500), () {
      _ref
          .read(mapLocationProvider.notifier)
          .updateMarkerPosition(defaultSaudiLocation);
    });

    // Force marker to stay visible again after a longer delay
    Future.delayed(const Duration(seconds: 2), () {
      _ref
          .read(mapLocationProvider.notifier)
          .updateMarkerPosition(defaultSaudiLocation);
    });
  }

  // Set the map controller
  void setMapController(GoogleMapController controller) {
    log("Setting map controller");
    // Update the map controller in state and set it for the custom info window in a single operation
    state = state.copyWith(mapController: controller);
    state.customInfoWindowController.googleMapController = controller;

    // Get the current location from the map provider
    final mapState = _ref.read(mapLocationProvider);
    if (mapState.currentLocation != null) {
      log("Current location available: ${mapState.currentLocation}");
      // If we already have an address, just show the info window
      // Otherwise, get the address first
      if (state.currentAddress != null) {
        log("Address already available: ${state.currentAddress}");
        // Show the info window after a short delay to ensure the map is ready
        Future.delayed(const Duration(milliseconds: 300), () {
          showCustomInfoWindow(mapState.currentLocation!);
        });
      } else {
        log("Getting address from current location");
        // Get the address and then show the info window
        getAddressFromLatLng(mapState.currentLocation!);
      }
    }
  }

  // Show the custom info window with the address
  void showCustomInfoWindow(LatLng position) {
    // Make sure we have a map controller
    if (state.mapController == null) {
      log("Cannot show info window: map controller is null");
      return;
    }

    try {
      log("showCustomInfoWindow called with state: streetName=${state.streetName}, cityName=${state.cityName}, countryName=${state.countryName}");
      log("Showing custom info window at $position with address: ${state.formattedAddress}");

      // Calculate a position slightly above the marker for better visibility
      final adjustedPosition = LatLng(
        position.latitude + 0.0012, // Small adjustment to position above marker
        position.longitude,
      );

      // Make sure the custom info window controller is properly set up
      if (state.customInfoWindowController.googleMapController == null) {
        log("Setting googleMapController for customInfoWindowController");
        state.customInfoWindowController.googleMapController =
            state.mapController;
      }

      // Hide any existing info window first
      state.customInfoWindowController.hideInfoWindow!();

      // Update the info window visibility state
      state = state.copyWith(isInfoWindowVisible: true);

      // Make sure the marker is still visible by updating it in the map provider
      _ref.read(mapLocationProvider.notifier).updateMarkerPosition(position);

      // Use a small delay to ensure the info window is properly displayed
      Future.delayed(const Duration(milliseconds: 300), () {
        try {
          log("Adding info window at position: $adjustedPosition");
          // Show the custom info window at the adjusted position
          // Use _createInfoWindowContent to ensure we get a fresh instance
          // with the latest address
          state.customInfoWindowController.addInfoWindow!(
            _createInfoWindowContent(),
            adjustedPosition,
          );
          log("Info window added successfully");
        } catch (e) {
          log("Error adding info window: $e");
          // If there's an error, try again with a longer delay
          Future.delayed(const Duration(milliseconds: 500), () {
            try {
              state.customInfoWindowController.addInfoWindow!(
                _createInfoWindowContent(),
                adjustedPosition,
              );
              log("Info window added successfully on retry");
            } catch (e) {
              log("Error adding info window on retry: $e");
              state = state.copyWith(isInfoWindowVisible: false);
            }
          });
        }
      });
    } catch (e) {
      log("Error showing custom info window: $e");
      // If there's an error, update the state
      state = state.copyWith(isInfoWindowVisible: false);
    }
  }

  // Get address from latitude and longitude
  Future<bool> getAddressFromLatLng(LatLng position) async {
    log("Getting address from lat/lng: $position");
    // Don't set loading state to true here to avoid hiding the map and marker
    // state = state.copyWith(isLoading: true);

    try {
      // Get placemarks from coordinates
      List<Placemark> placemarks = await placemarkFromCoordinates(
        position.latitude,
        position.longitude,
      );

      if (placemarks.isNotEmpty) {
        Placemark place = placemarks[0];
        log("Got placemark: ${place.name}, ${place.street}, ${place.locality}, ${place.country}");

        // Create a more detailed address string
        final List<String?> addressComponents = [
          place.name,
          place.street,
          place.subLocality,
          place.locality,
          place.postalCode,
          place.administrativeArea,
          place.country,
        ];

        final List<String> addressParts = addressComponents
            .where((part) => part != null && part.isNotEmpty)
            .map((part) => part!)
            .toList();

        final currentAddress = addressParts.join(', ');
        final streetName = place.street ?? '';
        final cityName = place.locality ?? '';
        final countryName = place.country ?? '';

        log("Formatted address: $currentAddress");

        // Update the form provider with the new values
        final formController = _ref.read(addAddressFormProvider.notifier);

        // Update form fields in a single batch
        formController.updateText('address', currentAddress);
        formController.updateText('house_no_street', streetName);
        formController.updateText('latitude', position.latitude.toString());
        formController.updateText('longitude', position.longitude.toString());

        // Update the state with the new address information
        log("Before state update: streetName=${state.streetName}, cityName=${state.cityName}, countryName=${state.countryName}");

        state = state.copyWith(
          currentAddress: currentAddress,
          streetName: streetName,
          cityName: cityName,
          countryName: countryName,
          // Don't change loading state here
        );

        // Log the updated state
        log("After state update: streetName=${state.streetName}, cityName=${state.cityName}, countryName=${state.countryName}");
        log("Updated state with new address: ${state.formattedAddress}");

        // Make sure the map controller is set
        if (state.mapController != null) {
          // Move the camera to the position
          state.mapController!
              .animateCamera(
            CameraUpdate.newLatLngZoom(position, 15),
          )
              .then((_) {
            // Show the info window after camera animation completes
            // This ensures the info window is displayed in the correct position
            showCustomInfoWindow(position);
          });
        }

        // Return true to indicate success
        return true;
      } else {
        log("No placemarks found for position: $position");
      }
    } catch (e) {
      // Log the error
      log('Error getting address: $e');
    }

    // Don't update loading state here
    // state = state.copyWith(isLoading: false);

    // Return false if we couldn't get the address
    return false;
  }

  // Format the address to show only street, city, and country
  String formatSimpleAddress() {
    // Log the current state for debugging
    log("Formatting address with state: street=${state.streetName}, city=${state.cityName}, country=${state.countryName}");

    // Use the getter from the state class
    final address = state.formattedAddress;
    log("Formatted address: $address");
    return address;
  }

  // Function to build the custom info window content
  Widget Function() _customInfoWindowBuilder = () => Container();

  // Set the custom info window content builder
  void setCustomInfoWindowContent(Widget Function() builder) {
    _customInfoWindowBuilder = builder;
    log("Custom info window content builder set");
  }

  // Build the custom info window content
  Widget buildCustomInfoWindowContent() {
    log("Building custom info window content");
    final content = _customInfoWindowBuilder();
    return content;
  }

  // This method is called by the showCustomInfoWindow method
  // to create a new instance of the info window content
  Widget _createInfoWindowContent() {
    // Log the current state values
    log("Current state values: streetName=${state.streetName}, cityName=${state.cityName}, countryName=${state.countryName}");

    // Get the current formatted address from the state
    final address = state.formattedAddress;
    log("Creating info window content with address: $address");

    // Force a new instance of CustomInfoWindowView to be created
    // with the current address
    return CustomInfoWindowView(address: address);
  }
}

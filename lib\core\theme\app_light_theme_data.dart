import 'package:flutter/material.dart';

import '../resources/app_colors.dart';
import 'app_text_theme.dart';

ThemeData appLightThemeData() {
  return ThemeData(
    colorScheme: const ColorScheme(
      primaryContainer: Color.fromRGBO(50, 45, 120, 1),
      primary: Color.fromRGBO(50, 45, 120, 1),
      secondary: Color.fromRGBO(34, 193, 224, 1),
      surface: Colors.black,
      error: Color.fromRGBO(241, 95, 109, 1),
      onPrimary: Colors.black,
      onSecondary: Colors.black,
      onSurface: Colors.black,
      onError: Colors.black,
      brightness: Brightness.light,
    ),
    brightness: Brightness.light,

    scaffoldBackgroundColor: Colors.white,

    progressIndicatorTheme: const ProgressIndicatorThemeData(
      color: Colors.black,
    ),

    /// AppBar theme
    appBarTheme: AppBarTheme(
      centerTitle: true,
      color: Colors.white,
      elevation: 0,
      titleTextStyle: appTextTheme.headlineSmall?.copyWith(
        color: const Color.fromRGBO(50, 45, 120, 1),
        fontWeight: FontWeight.w700,
      ),
      actionsIconTheme: const IconThemeData(
        color: Color.fromRGBO(50, 45, 120, 1),
      ),
      iconTheme: const IconThemeData(
        color: Color.fromRGBO(50, 45, 120, 1),
      ),
    ),

    /// Button theme
    buttonTheme: const ButtonThemeData(
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(8))),
      disabledColor: Color.fromRGBO(34, 193, 224, 0.1),
      buttonColor: Color.fromRGBO(34, 193, 224, 1),
    ),

    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        disabledBackgroundColor: AppColors.disabled,
        disabledForegroundColor: AppColors.lightSlateGray,
        fixedSize: const Size(double.maxFinite, 48),
        foregroundColor: Colors.white,
        backgroundColor: AppColors.primaryColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        elevation: 0,
      ),
    ),

    popupMenuTheme: PopupMenuThemeData(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      textStyle: appTextTheme.titleSmall?.copyWith(
        color: Colors.black,
      ),
      color: Colors.white,
    ),

    /// Text theme
    textTheme: appTextTheme,
    bottomNavigationBarTheme:  BottomNavigationBarThemeData(
      backgroundColor: AppColors.white,
      selectedItemColor: AppColors.primaryColor,
      unselectedItemColor: AppColors.slateGray,
    ),
    bottomSheetTheme: const BottomSheetThemeData(
      backgroundColor: AppColors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
    ),
    inputDecorationTheme: InputDecorationTheme(
      labelStyle: appTextTheme.bodySmall?.textColor,
      errorStyle: appTextTheme.bodySmall?.crimsonRed,
      hintStyle: appTextTheme.bodySmall?.textColor,
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: AppColors.lightGray, width: 1),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(
          color: AppColors.crimsonRed,
          width: 1,
        ),
      ),
      floatingLabelStyle: appTextTheme.labelLarge?.copyWith(
        color: const Color.fromRGBO(50, 45, 120, 1),
      ),
      isDense: true,
      iconColor: const Color.fromRGBO(34, 193, 224, 1),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(
          color: AppColors.borderColor,
          width: 1,
        ),
      ),

      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: AppColors.lightGray, width: 1),
      ),
    disabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: AppColors.lightGray, width: 1),
      ),
    ),
    tabBarTheme: TabBarTheme(
      indicatorSize: TabBarIndicatorSize.tab,
      labelStyle: appTextTheme.labelMedium,
      labelColor: AppColors.textColor,
      unselectedLabelColor: AppColors.lightSlateGray,
      unselectedLabelStyle: appTextTheme.labelMedium,
      indicator: BoxDecoration(
        borderRadius: BorderRadius.circular(6),
        color: AppColors.white,
      ),
      dividerColor: Colors.transparent,
    ),

  );
}

//

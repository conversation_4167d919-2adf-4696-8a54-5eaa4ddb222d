import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:zod/core/app_theme.dart';

import 'package:zod/core/resources/app_colors.dart';

class CategoryBadgeView extends StatelessWidget {
  const CategoryBadgeView({super.key, required this.title});
  final String title;
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: AppColors.iceBlue,
        border: Border.all(color: AppColors.softSkyBlue)
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 8,
          vertical: 4,
        ),
        child: Text(
         title,
          style: context.textTheme.labelMedium?.deepBlue,
        ),
      ),
    );
  }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('place_favorites', function (Blueprint $table) {
            // Drop the existing unique constraint
            $table->dropUnique(['user_id', 'place_id']);
        });

        // Create a partial unique index that only applies to non-deleted records
        // This allows multiple soft-deleted records but only one active record per user-place combination
        DB::statement('CREATE UNIQUE INDEX place_favorites_user_id_place_id_active_unique ON place_favorites (user_id, place_id) WHERE deleted_at IS NULL');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop the partial unique index
        DB::statement('DROP INDEX IF EXISTS place_favorites_user_id_place_id_active_unique');

        Schema::table('place_favorites', function (Blueprint $table) {
            // Restore the original unique constraint
            $table->unique(['user_id', 'place_id']);
        });
    }
};

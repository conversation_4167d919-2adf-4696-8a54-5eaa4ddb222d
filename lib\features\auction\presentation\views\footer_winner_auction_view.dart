import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:zod/core/localizations/app_localizations_extension.dart';

import 'package:zod/core/resources/app_icons.dart';

class FooterWinnerAuctionView extends StatelessWidget {
  const FooterWinnerAuctionView({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Skeleton.ignore(child: SvgPicture.asset(AppIcons.cool)),
        SizedBox(width: 8),
        Text(
          context.localizations.you_won_the_auction,
          style: context.textTheme.labelLarge,
        ),
      ],
    );
  }
}

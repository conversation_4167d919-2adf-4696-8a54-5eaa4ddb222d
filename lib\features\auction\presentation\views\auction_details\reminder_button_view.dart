import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/core/resources/app_icons.dart';

import 'package:zod/features/auction/data/models/auction.dart';
import 'package:zod/features/auction/presentation/providers/auction_provider.dart';

class ReminderButtonView extends ConsumerWidget {
  final Auction auction;

  const ReminderButtonView({
    Key? key,
    required this.auction,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Only show the reminder button for upcoming auctions that the user has joined
    if (auction.isJoined == false) {
      return const SizedBox.shrink();
    }

    final auctionState = ref.watch(auctionProvider(auction.id));

    return auctionState.isLoadingReminder
        ? _buildLoadingButton()
        : _buildReminderButton(
            context, ref, auctionState.auction?.isReminderEnabled ?? false);
    // return auctionState.when(
    //   data: (isEnabled) {
    //     return ;
    //   },
    //   loading: () => _buildLoadingButton(),
    //   error: (error, _) => _buildErrorButton(context),
    // );
  }

  Widget _buildReminderButton(
      BuildContext context, WidgetRef ref, bool isEnabled) {
    return InkWell(
      onTap: () {
        ref.read(auctionProvider(auction.id).notifier).toggleReminder();
      },
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: isEnabled ? AppColors.royalBlue : AppColors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isEnabled ? AppColors.royalBlue : AppColors.periwinkle,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SvgPicture.asset(
              isEnabled ? AppIcons.reminderOn : AppIcons.reminderOff,
              // color: isEnabled ? AppColors.white : AppColors.steelGray,
              width: 20,
              height: 20,
            ),
            const SizedBox(width: 4),
            Text(
              isEnabled
                  ? context.localizations.reminder_on
                  : context.localizations.reminder_off,
              style: context.textTheme.labelMedium?.copyWith(
                color: isEnabled ? AppColors.white : AppColors.primaryColor,
                fontWeight: isEnabled ? FontWeight.w600 : FontWeight.w400,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingButton() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.softSteel),
      ),
      child: const SizedBox(
        width: 20,
        height: 20,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.royalBlue),
        ),
      ),
    );
  }
}

import 'package:bond_network/bond_network.dart';
import 'package:json_annotation/json_annotation.dart';

part 'extra_time.g.dart';

@JsonSerializable(explicitToJson: true)
class ExtraTime extends Model {
  @J<PERSON><PERSON><PERSON>(name: 'user_id')
  final int userId;
  @J<PERSON><PERSON><PERSON>(name: 'auction_id')
  final int auctionId;
  @Json<PERSON>ey(name: 'issue_reason')
  final String issueReason;
  final String description;
  @JsonKey(name: 'requested_hours')
  final int requestedHours;

  const ExtraTime({
    required super.id,
    required this.userId,
    required this.auctionId,
    required this.issueReason,
    required this.requestedHours,
    required this.description,
  });

  factory ExtraTime.fromJson(Map<String, dynamic> json) =>
      _$ExtraTimeFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$ExtraTimeToJson(this);

  @override
  List<Object?> get props => [auctionId];
}

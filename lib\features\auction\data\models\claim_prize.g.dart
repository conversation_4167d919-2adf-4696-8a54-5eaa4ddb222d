// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'claim_prize.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ClaimPrize _$ClaimPrizeFromJson(Map<String, dynamic> json) => ClaimPrize(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      image: json['image'] as String,
      finalBidPoints: (json['final_bid_points'] as num).toInt(),
      productCost: (json['product_cost'] as num).toInt(),
      pointsValue: (json['points_value'] as num).toInt(),
      remainingToPay: (json['remaining_to_pay'] as num).toInt(),
      taxPercentage: json['tax_percentage'] as String,
      taxAmount: (json['tax_amount'] as num).toDouble(),
      totalToPay: (json['total_to_pay'] as num).toDouble(),
      claimBefore: json['claim_prize_before'] == null
          ? null
          : DateTime.parse(json['claim_prize_before'] as String),
    );

Map<String, dynamic> _$ClaimPrizeToJson(ClaimPrize instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'image': instance.image,
      'final_bid_points': instance.finalBidPoints,
      'points_value': instance.pointsValue,
      'product_cost': instance.productCost,
      'remaining_to_pay': instance.remainingToPay,
      'tax_percentage': instance.taxPercentage,
      'tax_amount': instance.taxAmount,
      'total_to_pay': instance.totalToPay,
      'claim_prize_before': instance.claimBefore?.toIso8601String(),
    };

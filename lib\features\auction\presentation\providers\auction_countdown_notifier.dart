import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:get_it/get_it.dart';
import 'package:zod/core/utils/timezone_utils.dart';
import 'package:zod/features/auction/data/api.dart';
import 'package:zod/features/auction/data/models/auction.dart';

/// State class for the auction countdown
///
/// This class holds the state for the auction countdown, including:
/// - The formatted time string (HH:MM:SS)
/// - Whether the auction has ended
/// - Whether the auction ended event has been triggered
class AuctionCountdownState {
  /// The formatted time string in HH:MM:SS format
  final String formattedTime;

  /// Whether the auction has ended
  final bool isEnded;

  /// Whether the auction ended event has been triggered
  final bool hasTriggeredEndEvent;

  const AuctionCountdownState({
    required this.formattedTime,
    required this.isEnded,
    required this.hasTriggeredEndEvent,
  });

  /// Initial state with default values
  factory AuctionCountdownState.initial() => const AuctionCountdownState(
        formattedTime: '00:00:00',
        isEnded: false,
        hasTriggeredEndEvent: false,
      );

  /// Create a copy of this state with the given fields replaced
  AuctionCountdownState copyWith({
    String? formattedTime,
    bool? isEnded,
    bool? hasTriggeredEndEvent,
  }) {
    return AuctionCountdownState(
      formattedTime: formattedTime ?? this.formattedTime,
      isEnded: isEnded ?? this.isEnded,
      hasTriggeredEndEvent: hasTriggeredEndEvent ?? this.hasTriggeredEndEvent,
    );
  }
}

/// Notifier class that handles the auction countdown logic
///
/// This class is responsible for:
/// - Starting and managing the countdown timer
/// - Updating the formatted time string every second
/// - Triggering the auction ended event when the timer reaches zero
/// - Cleaning up resources when disposed
class AuctionCountdownNotifier extends StateNotifier<AuctionCountdownState> {
  /// The auction being counted down
  final Auction auction;

  /// Timer that updates the countdown every second
  Timer? _timer;

  /// Creates a new AuctionCountdownNotifier for the given auction
  /// and immediately starts the countdown
  AuctionCountdownNotifier(this.auction)
      : super(AuctionCountdownState.initial()) {
    _startCountdown();
  }

  /// Start the countdown timer
  void _startCountdown() {
    // If the auction has no end time, return
    if (auction.endedAt == null) {
      state = state.copyWith(
        isEnded: true,
        hasTriggeredEndEvent: true,
      );
      return;
    }

    // Check if the auction has already ended
    final now = TimezoneUtils.getCurrentLocalTime();
    final differenceInSeconds =
        TimezoneUtils.getSecondsDifference(auction.endedAt!, now);

    // If the auction has ended (completely ended with 0 seconds left)
    if (differenceInSeconds <= 0) {
      state = state.copyWith(
        formattedTime: '00:00:00',
        isEnded: true,
      );

      // Trigger the auction ended event if it hasn't been triggered yet
      if (!state.hasTriggeredEndEvent) {
        _triggerAuctionEnded(auction.id);
        state = state.copyWith(hasTriggeredEndEvent: true);
      }
      return;
    }

    // Format the initial time
    final formattedTime = _formatTime(differenceInSeconds);
    state = state.copyWith(formattedTime: formattedTime);

    // Start a timer to update the countdown every second
    _timer = Timer.periodic(const Duration(seconds: 1), (_) {
      _updateCountdown();
    });
  }

  /// Update the countdown timer
  void _updateCountdown() {
    if (auction.endedAt == null) return;

    final now = TimezoneUtils.getCurrentLocalTime();
    final differenceInSeconds =
        TimezoneUtils.getSecondsDifference(auction.endedAt!, now);

    // If the auction has ended (completely ended with 0 seconds left)
    if (differenceInSeconds <= 0) {
      _timer?.cancel();
      state = state.copyWith(
        formattedTime: '00:00:00',
        isEnded: true,
      );

      // Trigger the auction ended event if it hasn't been triggered yet
      if (!state.hasTriggeredEndEvent) {
        _triggerAuctionEnded(auction.id);
        state = state.copyWith(hasTriggeredEndEvent: true);
      }
      return;
    }

    // Update the formatted time
    final formattedTime = _formatTime(differenceInSeconds);
    state = state.copyWith(formattedTime: formattedTime);
  }

  /// Format the time in HH:MM:SS format
  String _formatTime(int seconds) {
    final hours = seconds ~/ 3600;
    final minutes = (seconds % 3600) ~/ 60;
    final remainingSeconds = seconds % 60;

    return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  /// Trigger the auction ended event
  Future<void> _triggerAuctionEnded(int auctionId) async {
    try {
      final api = GetIt.I<AuctionApi>();
      await api.triggerAuctionEnded(auctionId);
    } catch (e) {
      // Error handled silently
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }
}

/// Provider for the auction countdown
///
/// This provider creates and manages an [AuctionCountdownNotifier] for a specific auction.
/// It handles the countdown logic and provides the formatted time string to the UI.
///
/// Usage:
/// ```dart
/// final countdownState = ref.watch(auctionCountdownProvider(auction));
/// final formattedTime = countdownState.formattedTime;
/// ```
final auctionCountdownProvider = StateNotifierProvider.family<
    AuctionCountdownNotifier, AuctionCountdownState, Auction>(
  (ref, auction) => AuctionCountdownNotifier(auction),
);

import 'package:bond_core/bond_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:super_tooltip/super_tooltip.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/app_theme.dart';
import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/core/resources/app_icons.dart';
import 'package:zod/features/auction/data/models/claim_prize.dart';
import 'package:zod/features/auction/presentation/views/summary_row_view.dart';

class OrderSummarySectionView extends StatefulWidget {
  final ClaimPrize claimPrize;

  const OrderSummarySectionView({
    super.key,
    required this.claimPrize,
  });

  @override
  State<OrderSummarySectionView> createState() =>
      _OrderSummarySectionViewState();
}

class _OrderSummarySectionViewState extends State<OrderSummarySectionView> {
  // Controller for the tooltip
  final SuperTooltipController _tooltipController = SuperTooltipController();

  @override
  void dispose() {
    _tooltipController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          context.localizations.order_summary,
          style: context.textTheme.titleSmall?.w700,
        ),
        const SizedBox(height: 16),
        Container(
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: AppColors.softSilver),
          ),
          child: Column(
            children: [
              const SizedBox(height: 12),
              // Product Price
              SummaryRowView(
                label: context.localizations.product_price,
                value: "${widget.claimPrize.productCost}",
              ),
              const SizedBox(height: 12),

              // Auctions Bids
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Text(
                          context.localizations.auction_bids,
                          style: context.textTheme.bodyMedium,
                        ),
                        const SizedBox(width: 2),
                        SuperTooltip(
                          controller: _tooltipController,
                          showBarrier: true,
                          barrierColor: Colors.transparent,
                          popupDirection: TooltipDirection.up,
                          backgroundColor: AppColors.darkSteel,
                          hasShadow: false,
                          borderRadius: 4,
                          arrowLength: 6,
                          arrowBaseWidth: 16,
                          arrowTipDistance: 8,
                          verticalOffset: -8,
                          minimumOutsideMargin: 16,
                          content: Text(
                            'The amount spent in the auction',
                            style: context.textTheme.bodyMedium?.white,
                            textAlign: TextAlign.center,
                          ),
                          child: GestureDetector(
                            onTap: () async {
                              await _tooltipController.showTooltip();
                            },
                            child: SvgPicture.asset(AppIcons.infoTooltip),
                          ),
                        ),
                      ],
                    ),
                    Row(
                      children: [
                        Row(
                          children: [
                            SvgPicture.asset(AppIcons.coin),
                            const SizedBox(width: 2),
                            Text(
                              '${widget.claimPrize.finalBidPoints} = ',
                              style: context.textTheme.labelLarge,
                            ),
                          ],
                        ),
                        Row(
                          children: [
                            SvgPicture.asset(AppIcons.riyal),
                            const SizedBox(width: 2),
                            Text(
                              '${widget.claimPrize.pointsValue}',
                              style: context.textTheme.labelLarge,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 12),

              Container(
                padding: EdgeInsets.zero,
                decoration: BoxDecoration(
                  color: AppColors.softWhite,
                ),
                child: Column(
                  children: [
                    const SizedBox(height: 5),
                    // Remaining to Pay
                    SummaryRowView(
                      label: context.localizations.remaining_to_pay,
                      value: "${widget.claimPrize.remainingToPay}",
                    ),
                    const Divider(
                      color: AppColors.softSilver,
                      thickness: 1,
                    ),
                    // Tax
                    SummaryRowView(
                      label: context.localizations
                          .tax(widget.claimPrize.taxPercentage.toString()),
                      value: "${widget.claimPrize.taxAmount}",
                    ),
                    const Divider(
                      color: AppColors.softSilver,
                      thickness: 1,
                    ),
                    // Total To Pay
                    SummaryRowView(
                      label: context.localizations.total_to_pay,
                      value: "${widget.claimPrize.totalToPay}",
                      isTotal: true,
                    ),
                    const SizedBox(height: 12),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/app/routes.dart';
import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/core/widgets/app_button.dart';
import 'package:zod/features/auction/data/models/auction.dart';
import 'package:zod/features/auction/data/states/auction_state.dart';

class CompetitionClosedBottomNavView extends ConsumerWidget {
  const CompetitionClosedBottomNavView({required this.auction,required this.auctionState,    super.key});
  final AuctionState auctionState;
  final Auction auction;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      padding: EdgeInsets.only(left: 16, right: 16, top: 12, bottom: 10),
      decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(10),
            topRight: Radius.circular(10),
          ),
          boxShadow: [
            BoxShadow(
              blurRadius: 8,
              offset: Offset(0, -2),
              color: AppColors.blackShadow,
            ),
          ]),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (auction.prizeExpired == false)
          Row(
            children: [
              Expanded(
                child: AppButton(
                  title: 'Explore More',
                  textColor: AppColors.steelGray,
                  backgroundColor: AppColors.disabled,
                  borderColor: AppColors.softSteel,
                  onPressed: () {
                    goRouter.go('/home');
                  },
                ),
              ),
              Visibility(
                  visible: auction.isIamWinner, child: SizedBox(width: 10)),
              Visibility(
                visible: auctionState.auctionMeta?.result?.hasPrizeClaim  == false,
                child: Visibility(
                  visible: auction.isIamWinner,
                  child: Expanded(
                    child: AppButton(
                      title: 'Claim Your Prize',
                      onPressed: () {
                        goRouter.push('/claim-prize/${auction.id}');
                      },
                    ),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(
            height: 8,
          )
        ],
      ),
    );
  }
}

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

class NetworkImageView extends StatelessWidget {
  const NetworkImageView(
    this.image, {
    super.key,
    this.height,
    this.width,
    this.fit = BoxFit.cover,
    this.radius = 12,
    this.borderColor = Colors.transparent,
    this.gradient,
    this.borderRadius,
    this.borderWidth = 0,
  });

  final String image;
  final double? height;
  final double? width;
  final BoxFit fit;
  final double radius;
  final double borderWidth;
  final Color borderColor;
  final Gradient? gradient;
  final BorderRadiusGeometry? borderRadius;
  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      width: width,
      decoration: BoxDecoration(
        borderRadius: borderRadius,
        border: Border.all(color: borderColor, width: borderWidth),
      ),
      child: ClipRRect(
        borderRadius: borderRadius ?? BorderRadius.zero,
        child: CachedNetworkImage(
          imageUrl: image,
          height: height,
          width: width,
          fit: fit,
          imageBuilder: (context, imageProvider) => Image(
            image: imageProvider,
            fit: fit,
            width: width,
            height: height,
          ),
          placeholder: (context, url) => Container(
            height: height ?? MediaQuery.of(context).size.height,
            width: width,
            color: Colors.grey[200],
          ),
          errorWidget: (context, url, error) => Container(
            color: Colors.grey[200],
            child: const Icon(Icons.image, color: Colors.grey),
          ),
        ),
      ),
    );
  }
}

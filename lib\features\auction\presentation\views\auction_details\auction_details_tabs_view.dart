import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zod/core/app_localizations.dart';
import 'package:zod/core/resources/app_colors.dart';
import 'package:zod/core/theme/app_text_theme.dart';
import 'package:zod/features/auction/data/models/auction.dart';
import 'package:zod/features/auction/presentation/views/auction_details/auction_result_view.dart';
import 'package:zod/features/auction/presentation/views/auction_details/live_auction_view.dart';
import 'package:zod/features/auction/presentation/views/auction_details/rules_auction_view.dart';
import 'package:zod/features/auth/presentation/providers/me_provider.dart';

import '../../providers/auction_provider.dart';
import 'about_auction_view.dart';
import 'empty_competition_view.dart';
import 'empty_participants_view.dart';

class AuctionDetailsTabsView extends ConsumerWidget {
  const AuctionDetailsTabsView(
      {super.key, required this.tabController, required this.auction});

  final TabController tabController;
  final Auction auction;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.watch(meProvider);
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Align(
              alignment: AlignmentDirectional.centerStart,
              child: TabBar(
                onTap: (index) => _onTap(index, ref),
                tabAlignment: TabAlignment.start,
                isScrollable: true,
                controller: tabController,
                labelColor: AppColors.darkSteel,
                unselectedLabelColor: AppColors.lightSlateGray,
                labelStyle: appTextTheme.labelMedium,
                unselectedLabelStyle: appTextTheme.labelMedium,
                indicatorSize: TabBarIndicatorSize.tab,
                indicator: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      color: Color.fromARGB(25, 10, 13, 18),
                      blurRadius: 2,
                      spreadRadius: -1,
                      offset: Offset(0, 1),
                    ),
                    BoxShadow(
                      color: Color.fromARGB(25, 10, 13, 18),
                      blurRadius: 3,
                      spreadRadius: 0,
                      offset: Offset(0, 1),
                    ),
                  ],
                ),
                tabs: [
                  Tab(
                    text: context.localizations.competition,
                    height: 27,
                  ),
                  // Tab(
                  //   text: context.localizations.bidders,
                  //   height: 27,
                  // ),
                  Tab(
                    text: context.localizations.about,
                    height: 27,
                  ),
                  Tab(
                    text: context.localizations.rules,
                    height: 27,
                  ),
                ],
              ),
            ),
          ),
          SizedBox(height: 12),
          Divider(color: AppColors.softSilver, height: 3),
          Flexible(
            child: TabBarView(
              controller: tabController,
              children: [
                _buildCompetitionTab(auction),
                // EmptyParticipantsView(),
                AboutAuctionView(auction: auction),
                RulesAuctionView(auction: auction),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _onTap(int index, WidgetRef ref) {
    switch (index) {
      case 2:
      case 3:
        ref
            .read(auctionProvider(int.parse(auction.id.toString())).notifier)
            .refreshAuction();
    }
  }

  Widget _buildCompetitionTab(Auction auction) {
    return Consumer(
      builder: (context, ref, child) {
        final auctionState = ref.watch(auctionProvider(auction.id));
        final auctionMeta = auctionState.auctionMeta;

        if ((auction.isClosed &&
                (auction.isJoined == true || auction.hasCurrentUserJoined)) ||
            auction.isIamWinner) {
          // Check if user won or lost
          return AuctionResultView(auction: auction, auctionMeta: auctionMeta);
        }

        // If auction is live and user joined
        if (auction.isLive) {
          return LiveAuctionView(auction: auction);
        }

        // Default view
        return EmptyCompetitionView(auction: auction);
      },
    );
  }
}

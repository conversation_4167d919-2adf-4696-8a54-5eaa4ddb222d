// ignore_for_file: non_constant_identifier_names

import 'package:bond_core/bond_core.dart';

class BidHistoryMeta with Jsonable {
  final bool success;

  final int auction_id;
  final int bids_count;
  final int current_page;
  final int last_page;
//  fromJson
  BidHistoryMeta(
      {required this.success,
      required this.auction_id,
      required this.bids_count,
      required this.current_page,
      required this.last_page});
  factory BidHistoryMeta.fromJson(Map<String, dynamic> json) {
    return BidHistoryMeta(
      success: json['success'],
      auction_id: json['auction_id'],
      bids_count: int.tryParse("${json['bids_count']}") ?? 0,
      current_page: json['current_page'],
      last_page: json['last_page'],
    );
  }
  @override
  Map<String, dynamic> toJson() => {
        'success': success,
        'auction_id': auction_id,
        'bids_count': bids_count,
        'current_page': current_page,
        'last_page': last_page,
      };
}

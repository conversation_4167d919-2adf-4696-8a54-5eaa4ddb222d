// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auction_meta.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AuctionMeta _$AuctionMetaFromJson(Map<String, dynamic> json) => AuctionMeta(
      success: json['success'] as bool?,
      message: json['message'] as String?,
      bidsCount: (json['bids_count'] as num?)?.toInt(),
      breakUntil: json['break_until'] == null
          ? null
          : DateTime.parse(json['break_until'] as String),
      result: json['result'] == null
          ? null
          : AuctionResult.fromJson(json['result'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$AuctionMetaToJson(AuctionMeta instance) =>
    <String, dynamic>{
      'success': instance.success,
      'message': instance.message,
      'bids_count': instance.bidsCount,
      'break_until': instance.breakUntil?.toIso8601String(),
      'result': instance.result?.toJson(),
    };

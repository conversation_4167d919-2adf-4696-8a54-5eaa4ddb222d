import 'package:bond_cache/bond_cache.dart';
import 'package:bond_network/bond_network.dart';
import 'package:zod/config/configs.dart';

class Api {
  static String get baseUrl => config('API_BASE_URL');

  static Map<String, String> headers() {
    return {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
      ...?Api.extraHeaders?.call(),
    };
  }

  static Future<String?> getToken() async {
    return Cache.get('token');
  }

  static Function()? extraHeaders;
}

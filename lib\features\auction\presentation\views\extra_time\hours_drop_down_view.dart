import 'package:bond_form/bond_form.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class HoursDropdown extends ConsumerWidget {
  final AsyncValue<List<DropDownItemState<int>>> hoursItems;
  final int? selectedValue;
  final String? hintText;
  final String? errorText;
  final Function(int?) onChanged;

  const HoursDropdown({
    super.key,
    required this.hoursItems,
    required this.selectedValue,
    required this.onChanged,
    this.hintText,
    this.errorText,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return hoursItems.when(
      data: (items) {
        return DropdownButtonFormField<int>(
          value: selectedValue,
          hint: Text(hintText ?? 'Select hours'),
          decoration: InputDecoration(
            errorText: errorText,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
          items: items.map((item) {
            return DropdownMenuItem<int>(
              value: item.value,
              child: Text(
                item.label,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            );
          }).toList(),
          onChanged: onChanged,
        );
      },
      loading: () => const LinearProgressIndicator(),
      error: (error, stack) => Text('Error loading hours: $error'),
    );
  }
}

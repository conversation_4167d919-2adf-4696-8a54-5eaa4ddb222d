import 'package:bond_app_analytics/bond_app_analytics.dart';

class AutobidOnEvent extends AnalyticsEvent {
  final int auctionId;
  final String auctionName;

  AutobidOnEvent({
    required this.auctionId,
    required this.auctionName,
  });

  @override
  String get key => 'autobid_on';

  @override
  Map<String, dynamic> get params => {
        'auction_id': auctionId,
        'auction_name': auctionName,
      };
}

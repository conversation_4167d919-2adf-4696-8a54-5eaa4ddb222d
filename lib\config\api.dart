const apiConfigs = {
  'API_BASE_URL': String.fromEnvironment('API_BASE_URL'),
  'CONNECT_TIMEOUT': int.fromEnvironment('CONNECT_TIMEOUT', defaultValue: 10000),
  'SEND_TIMEOUT': int.fromEnvironment('SEND_TIMEOUT'),
  'RECEIVE_TIMEOUT': int.fromEnvironment('RECEIVE_TIMEOUT'),
  'RECEIVE_DATA_WHEN_STATUS_ERROR':
      bool.fromEnvironment('RECEIVE_DATA_WHEN_STATUS_ERROR'),
      'PUSHER_APP_KEY': String.fromEnvironment('PUSHER_APP_KEY'),
      'PUSHER_APP_CLUSTER': String.fromEnvironment('PUSHER_APP_CLUSTER'),
      'PUSHER_HOST': String.fromEnvironment('PUSHER_HOST'),
      'PUSHER_SCHEME': String.fromEnvironment('PUSHER_SCHEME'),
      'PUSHER_PORT': String.fromEnvironment('PUSHER_PORT'),
      'PAYTABS_PROFILE_ID': String.fromEnvironment('PAYTABS_PROFILE_ID'),
      'PAYTABS_SERVER_KEY': String.fromEnvironment('PAYTABS_SERVER_KEY'),

      'PAYTABS_CLIENT_KEY': String.fromEnvironment('PAYTABS_CLIENT_KEY'),
      'PAYTABS_MERCHANT_COUNTRY_CODE': String.fromEnvironment('PAYTABS_MERCHANT_COUNTRY_CODE'),
      'PAYTABS_APPLE_PAY_IDENTIFIER': String.fromEnvironment('PAYTABS_APPLE_PAY_IDENTIFIER'),
};

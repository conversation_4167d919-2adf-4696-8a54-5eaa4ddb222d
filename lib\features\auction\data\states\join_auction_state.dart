import 'package:equatable/equatable.dart';

class JoinAuctionState extends Equatable {
  final bool isLoading;
  final String? error;

  const JoinAuctionState({
    this.isLoading = false,
    this.error,
  });

  @override
  List<Object?> get props => [isLoading, error];

  JoinAuctionState copyWith({
    bool? isLoading,
    String? error,
  }) {
    return JoinAuctionState(
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
    );
  }
}
